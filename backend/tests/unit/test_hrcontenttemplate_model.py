"""Unit tests for HRContentTemplate model."""
import pytest
import json
from datetime import datetime
from models import HRContentTemplate
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestHRContentTemplateModel:
    """Test per il modello HRContentTemplate (HR Assistant Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(HRContentTemplate)
    def test_table_exists(self):
        """Verifica che la tabella hr_content_templates esista"""
        assert_table_exists(HRContentTemplate)
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_creation_basic(self):
        """Test creazione base di un template contenuto HR"""
        safe_data = create_safe_data(
            HRContentTemplate,
            name='Employee Onboarding Checklist',
            category='onboarding',
            description='Template for generating comprehensive onboarding checklists',
            prompt_template='Generate an onboarding checklist for a {role} position in {department}...'
        )
        
        template = HRContentTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        assert template.id is not None
        assert template.name == 'Employee Onboarding Checklist'
        assert template.category == 'onboarding'
        assert template.usage_count == 0  # Default value
        assert template.is_active is True  # Default value
        assert template.created_at is not None
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_with_fields(self):
        """Test template con campi required e output format"""
        required_fields = [
            {'name': 'employee_name', 'type': 'string', 'required': True},
            {'name': 'department', 'type': 'string', 'required': True},
            {'name': 'start_date', 'type': 'date', 'required': True},
            {'name': 'manager_name', 'type': 'string', 'required': False}
        ]
        
        output_format = """
        # Welcome Letter for {employee_name}
        
        Dear {employee_name},
        
        Welcome to the {department} team! Your first day is {start_date}.
        Your manager {manager_name} will contact you before your start date.
        
        Best regards,
        HR Team
        """
        
        safe_data = create_safe_data(
            HRContentTemplate,
            name='Welcome Letter Template',
            category='onboarding',
            description='Template for generating personalized welcome letters',
            prompt_template='Create a welcome letter for {employee_name} joining {department}...',
            required_fields=json.dumps(required_fields),
            output_format=output_format
        )
        
        template = HRContentTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        assert template.required_fields == json.dumps(required_fields)
        assert template.output_format == output_format
        assert '{employee_name}' in template.output_format
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_categories(self):
        """Test diverse categorie di template"""
        template_data = [
            ('onboarding', 'New Employee Welcome Package'),
            ('offboarding', 'Exit Interview Questions'),
            ('performance', 'Performance Review Template'),
            ('training', 'Training Program Outline'),
            ('policies', 'Company Policy Document'),
            ('benefits', 'Benefits Explanation Guide'),
            ('communications', 'Team Announcement Template')
        ]
        
        templates = []
        for category, name in template_data:
            safe_data = create_safe_data(
                HRContentTemplate,
                name=name,
                category=category,
                prompt_template=f'Generate content for {category} category...'
            )
            
            template = HRContentTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica tutte le categorie
        for template, (expected_category, expected_name) in zip(templates, template_data):
            assert template.category == expected_category
            assert template.name == expected_name
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_usage_tracking(self):
        """Test tracking utilizzo template"""
        safe_data = create_safe_data(
            HRContentTemplate,
            name='Usage Tracking Test',
            category='test',
            prompt_template='Test template...'
        )
        
        template = HRContentTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        # Stato iniziale
        assert template.usage_count == 0
        assert template.last_used is None
        
        # Simula utilizzo
        template.usage_count = 5
        template.last_used = datetime.utcnow()
        db.session.commit()
        
        assert template.usage_count == 5
        assert template.last_used is not None
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_complex_prompt(self):
        """Test template con prompt complesso"""
        complex_prompt = """
        You are an HR specialist creating content for {content_type}.
        
        Context:
        - Company: {company_name}
        - Industry: {industry}
        - Company size: {company_size}
        - Target audience: {target_audience}
        
        Requirements:
        - Tone: {tone} (professional, friendly, formal)
        - Length: {length} (short, medium, long)
        - Include legal compliance for {jurisdiction}
        
        Generate {content_type} that includes:
        1. Clear introduction
        2. Main content sections
        3. Action items if applicable
        4. Contact information
        
        Additional notes: {additional_notes}
        """
        
        required_fields = [
            'content_type', 'company_name', 'industry', 'company_size',
            'target_audience', 'tone', 'length', 'jurisdiction', 'additional_notes'
        ]
        
        safe_data = create_safe_data(
            HRContentTemplate,
            name='Universal HR Content Generator',
            category='general',
            description='Flexible template for generating various HR content types',
            prompt_template=complex_prompt,
            required_fields=json.dumps(required_fields)
        )
        
        template = HRContentTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        assert len(template.prompt_template) > 500
        assert '{content_type}' in template.prompt_template
        assert '{company_name}' in template.prompt_template
        assert len(json.loads(template.required_fields)) == 9
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_active_status(self):
        """Test gestione stato attivo/inattivo"""
        safe_data = create_safe_data(
            HRContentTemplate,
            name='Active Status Test',
            category='test',
            prompt_template='Test template for status...'
        )
        
        template = HRContentTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        # Stato iniziale
        assert template.is_active is True
        
        # Disattiva
        template.is_active = False
        db.session.commit()
        assert template.is_active is False
        
        # Riattiva
        template.is_active = True
        db.session.commit()
        assert template.is_active is True
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_to_dict(self):
        """Test serializzazione to_dict"""
        required_fields = ['name', 'position', 'department']
        last_used_time = datetime.utcnow()
        
        safe_data = create_safe_data(
            HRContentTemplate,
            name='Job Description Generator',
            category='recruitment',
            description='Generate comprehensive job descriptions',
            prompt_template='Create a job description for {position} in {department}...',
            required_fields=json.dumps(required_fields),
            output_format='Formatted job description output',
            usage_count=15,
            last_used=last_used_time,
            is_active=True
        )
        
        template = HRContentTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        data = template.to_dict()
        
        assert data['id'] == template.id
        assert data['name'] == 'Job Description Generator'
        assert data['category'] == 'recruitment'
        assert data['description'] == 'Generate comprehensive job descriptions'
        assert data['required_fields'] == required_fields
        assert data['output_format'] == 'Formatted job description output'
        assert data['usage_count'] == 15
        assert data['is_active'] is True
        assert 'last_used' in data
        assert 'created_at' in data
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_validation(self):
        """Test validazione campi required"""
        # Test senza name (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                HRContentTemplate,
                category='test',
                prompt_template='Template without name'
                # name mancante
            )
            template = HRContentTemplate(**safe_data)
            db.session.add(template)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza category (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                HRContentTemplate,
                name='Template without category',
                prompt_template='Template without category'
                # category mancante
            )
            template = HRContentTemplate(**safe_data)
            db.session.add(template)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza prompt_template (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                HRContentTemplate,
                name='Template without prompt',
                category='test'
                # prompt_template mancante
            )
            template = HRContentTemplate(**safe_data)
            db.session.add(template)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(HRContentTemplate)
    def test_hr_content_template_deletion(self):
        """Test eliminazione template"""
        safe_data = create_safe_data(
            HRContentTemplate,
            name='To Delete Template',
            category='test',
            prompt_template='This template will be deleted'
        )
        
        template = HRContentTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        template_id = template.id
        
        # Elimina
        db.session.delete(template)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(HRContentTemplate, template_id)
        assert deleted is None