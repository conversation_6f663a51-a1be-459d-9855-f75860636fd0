"""Unit tests for ScheduledTask model."""
import pytest
from datetime import datetime, timedelta
from models import ScheduledTask, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestScheduledTaskModel:
    """Test per il modello ScheduledTask (CEO <PERSON> Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(ScheduledTask)
    def test_table_exists(self):
        """Verifica che la tabella scheduled_tasks esista"""
        assert_table_exists(ScheduledTask)
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_creation_basic(self):
        """Test creazione base di un task schedulato"""
        safe_data = create_safe_data(
            ScheduledTask,
            title='Weekly Business Review',
            description='Automated weekly review of key business metrics',
            task_type='business_review',
            frequency='weekly',
            created_by=self.user.id
        )
        
        task = ScheduledTask(**safe_data)
        db.session.add(task)
        db.session.commit()
        
        assert task.id is not None
        assert task.title == 'Weekly Business Review'
        assert task.task_type == 'business_review'
        assert task.frequency == 'weekly'
        assert task.status == 'active'  # Default value
        assert task.created_by == self.user.id
        assert task.created_at is not None
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_with_configuration(self):
        """Test task con configurazione completa"""
        config_params = {
            'include_financials': True,
            'include_kpis': True,
            'report_format': 'executive_summary',
            'email_recipients': ['<EMAIL>', '<EMAIL>']
        }
        
        data_sources = [
            'projects',
            'timesheets', 
            'financials',
            'personnel',
            'crm'
        ]
        
        next_run_time = datetime.utcnow() + timedelta(days=7)
        
        safe_data = create_safe_data(
            ScheduledTask,
            title='Monthly Risk Analysis',
            description='Comprehensive monthly risk assessment across all business areas',
            task_type='risk_analysis',
            frequency='monthly',
            cron_expression='0 9 1 * *',  # First day of month at 9 AM
            next_run=next_run_time,
            config_params=config_params,
            data_sources=data_sources,
            created_by=self.user.id
        )
        
        task = ScheduledTask(**safe_data)
        db.session.add(task)
        db.session.commit()
        
        assert task.task_type == 'risk_analysis'
        assert task.frequency == 'monthly'
        assert task.cron_expression == '0 9 1 * *'
        assert task.config_params == config_params
        assert task.data_sources == data_sources
        assert task.next_run == next_run_time
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_frequencies(self):
        """Test diverse frequenze di scheduling"""
        frequencies = [
            ('daily', 'Daily KPI Monitoring'),
            ('weekly', 'Weekly Team Performance'),
            ('monthly', 'Monthly Business Review'),
            ('quarterly', 'Quarterly Strategic Assessment')
        ]
        
        tasks = []
        for frequency, title in frequencies:
            safe_data = create_safe_data(
                ScheduledTask,
                title=title,
                task_type='monitoring',
                frequency=frequency,
                created_by=self.user.id
            )
            
            task = ScheduledTask(**safe_data)
            tasks.append(task)
            db.session.add(task)
        
        db.session.commit()
        
        # Verifica tutte le frequenze
        for task, (expected_frequency, expected_title) in zip(tasks, frequencies):
            assert task.frequency == expected_frequency
            assert task.title == expected_title
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_types(self):
        """Test diversi tipi di task"""
        task_types = [
            'business_review',
            'risk_analysis', 
            'performance_monitoring',
            'market_analysis',
            'compliance_check',
            'financial_report'
        ]
        
        tasks = []
        for i, task_type in enumerate(task_types):
            safe_data = create_safe_data(
                ScheduledTask,
                title=f'Task {i+1}: {task_type.replace("_", " ").title()}',
                task_type=task_type,
                frequency='weekly',
                created_by=self.user.id
            )
            
            task = ScheduledTask(**safe_data)
            tasks.append(task)
            db.session.add(task)
        
        db.session.commit()
        
        # Verifica tutti i tipi
        for task, expected_type in zip(tasks, task_types):
            assert task.task_type == expected_type
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_execution_tracking(self):
        """Test tracking delle esecuzioni"""
        last_run_time = datetime.utcnow() - timedelta(hours=2)
        
        last_result = {
            'status': 'success',
            'duration_seconds': 45.2,
            'records_processed': 1250,
            'insights_generated': 8,
            'alerts_triggered': 2
        }
        
        safe_data = create_safe_data(
            ScheduledTask,
            title='Performance Tracking Task',
            task_type='performance_monitoring',
            frequency='daily',
            last_run=last_run_time,
            last_result=last_result,
            average_duration_seconds=42.5,
            success_rate=0.95,
            created_by=self.user.id
        )
        
        task = ScheduledTask(**safe_data)
        db.session.add(task)
        db.session.commit()
        
        assert task.last_run == last_run_time
        assert task.last_result == last_result
        assert task.average_duration_seconds == 42.5
        assert task.success_rate == 0.95
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_status_workflow(self):
        """Test workflow degli stati del task"""
        safe_data = create_safe_data(
            ScheduledTask,
            title='Status Workflow Test',
            task_type='test',
            frequency='daily',
            created_by=self.user.id
        )
        
        task = ScheduledTask(**safe_data)
        db.session.add(task)
        db.session.commit()
        
        # Stato iniziale
        assert task.status == 'active'
        
        # Pausa
        task.status = 'paused'
        db.session.commit()
        assert task.status == 'paused'
        
        # Ripresa
        task.status = 'active'
        db.session.commit()
        assert task.status == 'active'
        
        # Fallimento
        task.status = 'failed'
        task.error_message = 'Database connection timeout'
        db.session.commit()
        
        assert task.status == 'failed'
        assert task.error_message == 'Database connection timeout'
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_cron_expressions(self):
        """Test espressioni cron diverse"""
        cron_configs = [
            ('0 9 * * 1', 'Every Monday at 9 AM'),
            ('0 0 1 * *', 'First day of every month'),
            ('0 */6 * * *', 'Every 6 hours'),
            ('0 9 * * 1-5', 'Weekdays at 9 AM')
        ]
        
        tasks = []
        for cron_expr, description in cron_configs:
            safe_data = create_safe_data(
                ScheduledTask,
                title=f'Cron Task: {description}',
                task_type='scheduled',
                frequency='custom',
                cron_expression=cron_expr,
                created_by=self.user.id
            )
            
            task = ScheduledTask(**safe_data)
            tasks.append(task)
            db.session.add(task)
        
        db.session.commit()
        
        # Verifica espressioni cron
        for task, (expected_cron, _) in zip(tasks, cron_configs):
            assert task.cron_expression == expected_cron
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_user_relationship(self):
        """Test relazione con User (creator)"""
        safe_data = create_safe_data(
            ScheduledTask,
            title='User Relationship Test',
            task_type='test',
            frequency='weekly',
            created_by=self.user.id
        )
        
        task = ScheduledTask(**safe_data)
        db.session.add(task)
        db.session.commit()
        
        # Verifica relazione
        assert task.creator is not None
        assert task.creator.id == self.user.id
        assert task.created_by == self.user.id
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_to_dict(self):
        """Test serializzazione to_dict"""
        last_run_time = datetime.utcnow() - timedelta(hours=1)
        next_run_time = datetime.utcnow() + timedelta(hours=23)
        
        safe_data = create_safe_data(
            ScheduledTask,
            title='Serialization Test Task',
            description='Testing serialization functionality',
            task_type='monitoring',
            frequency='daily',
            status='active',
            last_run=last_run_time,
            next_run=next_run_time,
            success_rate=0.92,
            created_by=self.user.id
        )
        
        task = ScheduledTask(**safe_data)
        db.session.add(task)
        db.session.commit()
        
        data = task.to_dict()
        
        assert data['id'] == task.id
        assert data['title'] == 'Serialization Test Task'
        assert data['description'] == 'Testing serialization functionality'
        assert data['task_type'] == 'monitoring'
        assert data['frequency'] == 'daily'
        assert data['status'] == 'active'
        assert 'last_run' in data
        assert 'next_run' in data
        assert data['success_rate'] == 0.92
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_validation(self):
        """Test validazione campi required"""
        # Test senza title (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                ScheduledTask,
                task_type='test',
                frequency='daily',
                created_by=self.user.id
                # title mancante
            )
            task = ScheduledTask(**safe_data)
            db.session.add(task)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza task_type (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                ScheduledTask,
                title='Invalid Task',
                frequency='daily',
                created_by=self.user.id
                # task_type mancante
            )
            task = ScheduledTask(**safe_data)
            db.session.add(task)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza frequency (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                ScheduledTask,
                title='Invalid Task',
                task_type='test',
                created_by=self.user.id
                # frequency mancante
            )
            task = ScheduledTask(**safe_data)
            db.session.add(task)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(ScheduledTask)
    def test_scheduled_task_deletion(self):
        """Test eliminazione task"""
        safe_data = create_safe_data(
            ScheduledTask,
            title='To Delete',
            task_type='test',
            frequency='daily',
            created_by=self.user.id
        )
        
        task = ScheduledTask(**safe_data)
        db.session.add(task)
        db.session.commit()
        task_id = task.id
        
        # Elimina
        db.session.delete(task)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(ScheduledTask, task_id)
        assert deleted is None