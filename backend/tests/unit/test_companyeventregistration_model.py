"""Unit tests for CompanyEventRegistration model."""
import pytest
from datetime import datetime, timedelta
from models import CompanyEventRegistration, CompanyEvent, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestCompanyEventRegistrationModel:
    """Test per il modello CompanyEventRegistration (Communication Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        
        # Crea un evento per i test
        with app.app_context():
            import time
            unique_title = f'Test Event {int(time.time() * 1000000)}'
            safe_data = create_safe_data(
                CompanyEvent,
                title=unique_title,
                description='Test event for registrations',
                start_time=datetime.utcnow() + timedelta(days=7),
                end_time=datetime.utcnow() + timedelta(days=7, hours=2),
                location='Conference Room A',
                max_participants=50,
                organizer_id=self.user.id
            )
            
            event = CompanyEvent(**safe_data)
            db.session.add(event)
            db.session.commit()
            self.event_id = event.id
    
    @requires_table_exists(CompanyEventRegistration)
    def test_table_exists(self):
        """Verifica che la tabella company_event_registrations esista"""
        assert_table_exists(CompanyEventRegistration)
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_creation_basic(self):
        """Test creazione base registrazione evento"""
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        assert registration.id is not None
        assert registration.event_id == self.event_id
        assert registration.user_id == self.user.id
        assert registration.status == 'registered'
        assert registration.registered_at is not None
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_status_workflow(self):
        """Test workflow stati registrazione"""
        statuses = ['registered', 'attended', 'cancelled']
        
        registrations = []
        for i, status in enumerate(statuses):
            safe_data = create_safe_data(
                CompanyEventRegistration,
                event_id=self.event_id,
                user_id=self.user.id + i + 1,  # Different users to avoid constraint
                status=status
            )
            
            registration = CompanyEventRegistration(**safe_data)
            registrations.append(registration)
            db.session.add(registration)
        
        db.session.commit()
        
        # Verifica tutti gli stati
        for registration, expected_status in zip(registrations, statuses):
            assert registration.status == expected_status
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_with_notes(self):
        """Test registrazione con note"""
        notes = "Partecipazione confermata. Richiede parcheggio riservato."
        
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered',
            notes=notes
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        assert registration.notes == notes
        assert len(registration.notes) > 20
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_event_relationship(self):
        """Test relazione con CompanyEvent"""
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        # Verifica relazione
        assert registration.event is not None
        assert registration.event.id == self.event_id
        assert registration.event_id == self.event_id
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_user_relationship(self):
        """Test relazione con User"""
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        # Verifica relazione
        assert registration.user is not None
        assert registration.user.id == self.user.id
        assert registration.user_id == self.user.id
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_unique_constraint(self):
        """Test vincolo unico user-event"""
        # Prima registrazione
        safe_data_1 = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration_1 = CompanyEventRegistration(**safe_data_1)
        db.session.add(registration_1)
        db.session.commit()
        
        # Tentativo di registrazione duplicata (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data_2 = create_safe_data(
                CompanyEventRegistration,
                event_id=self.event_id,
                user_id=self.user.id,  # Stesso utente e evento
                status='registered'
            )
            registration_2 = CompanyEventRegistration(**safe_data_2)
            db.session.add(registration_2)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_status_transitions(self):
        """Test transizioni di stato"""
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        # Registrato -> Partecipato
        registration.status = 'attended'
        db.session.commit()
        assert registration.status == 'attended'
        
        # Reset per test cancellazione
        registration.status = 'registered'
        db.session.commit()
        
        # Registrato -> Cancellato
        registration.status = 'cancelled'
        db.session.commit()
        assert registration.status == 'cancelled'
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_multiple_users_same_event(self):
        """Test registrazioni multiple utenti stesso evento"""
        user_statuses = [
            ('registered', None),
            ('attended', 'Presente per tutta la durata'),
            ('cancelled', 'Impegno imprevisto')
        ]
        
        registrations = []
        for i, (status, notes) in enumerate(user_statuses):
            safe_data = create_safe_data(
                CompanyEventRegistration,
                event_id=self.event_id,
                user_id=self.user.id + i + 1,  # Different users
                status=status,
                notes=notes
            )
            
            registration = CompanyEventRegistration(**safe_data)
            registrations.append(registration)
            db.session.add(registration)
        
        db.session.commit()
        
        # Verifica che tutte le registrazioni appartengano allo stesso evento
        for registration in registrations:
            assert registration.event_id == self.event_id
        
        # Verifica stati e note diverse
        for registration, (expected_status, expected_notes) in zip(registrations, user_statuses):
            assert registration.status == expected_status
            assert registration.notes == expected_notes
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_registration_time_tracking(self):
        """Test tracking tempo di registrazione"""
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        # Verifica che registered_at sia stato impostato
        assert registration.registered_at is not None
        assert isinstance(registration.registered_at, datetime)
        
        # Verifica che sia recente (entro 1 minuto)
        time_diff = datetime.utcnow() - registration.registered_at
        assert time_diff.total_seconds() < 60
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_to_dict(self):
        """Test serializzazione to_dict"""
        notes = "Registrazione con note complete"
        
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='attended',
            notes=notes
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        data = registration.to_dict()
        
        assert data['id'] == registration.id
        assert data['event_id'] == self.event_id
        assert data['user_id'] == self.user.id
        assert data['status'] == 'attended'
        assert data['notes'] == notes
        assert 'event' in data
        assert 'user' in data
        assert data['event']['id'] == self.event_id
        assert data['user']['id'] == self.user.id
        assert 'registered_at' in data
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_different_events(self):
        """Test registrazioni a eventi diversi"""
        # Crea un secondo evento
        with self.app.app_context():
            import time
            unique_title = f'Second Event {int(time.time() * 1000000)}'
            safe_data = create_safe_data(
                CompanyEvent,
                title=unique_title,
                description='Second test event',
                start_time=datetime.utcnow() + timedelta(days=14),
                end_time=datetime.utcnow() + timedelta(days=14, hours=3),
                location='Conference Room B',
                organizer_id=self.user.id
            )
            
            event2 = CompanyEvent(**safe_data)
            db.session.add(event2)
            db.session.commit()
            event2_id = event2.id
        
        # Registra lo stesso utente a due eventi diversi
        registrations = []
        
        for event_id in [self.event_id, event2_id]:
            safe_data = create_safe_data(
                CompanyEventRegistration,
                event_id=event_id,
                user_id=self.user.id,
                status='registered'
            )
            
            registration = CompanyEventRegistration(**safe_data)
            registrations.append(registration)
            db.session.add(registration)
        
        db.session.commit()
        
        # Verifica che l'utente sia registrato a entrambi gli eventi
        assert registrations[0].event_id == self.event_id
        assert registrations[1].event_id == event2_id
        assert registrations[0].user_id == registrations[1].user_id
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        
        repr_str = repr(registration)
        assert f'User:{self.user.id}' in repr_str
        assert f'Event:{self.event_id}' in repr_str
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_validation(self):
        """Test validazione campi required"""
        # Test senza event_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                CompanyEventRegistration,
                user_id=self.user.id,
                status='registered'
                # event_id mancante
            )
            registration = CompanyEventRegistration(**safe_data)
            db.session.add(registration)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza user_id (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                CompanyEventRegistration,
                event_id=self.event_id,
                status='registered'
                # user_id mancante
            )
            registration = CompanyEventRegistration(**safe_data)
            db.session.add(registration)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(CompanyEventRegistration)
    def test_company_event_registration_deletion(self):
        """Test eliminazione registrazione"""
        safe_data = create_safe_data(
            CompanyEventRegistration,
            event_id=self.event_id,
            user_id=self.user.id,
            status='registered'
        )
        
        registration = CompanyEventRegistration(**safe_data)
        db.session.add(registration)
        db.session.commit()
        registration_id = registration.id
        
        # Elimina
        db.session.delete(registration)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(CompanyEventRegistration, registration_id)
        assert deleted is None