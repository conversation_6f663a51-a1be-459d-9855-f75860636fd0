"""Unit tests for CoreCompetency model."""
import pytest
from models import CoreCompetency
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestCoreCompetencyModel:
    """Test per il modello CoreCompetency (Performance Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app):
        """Setup per ogni test method"""
        self.app = app
    
    @requires_table_exists(CoreCompetency)
    def test_table_exists(self):
        """Verifica che la tabella core_competencies esista"""
        assert_table_exists(CoreCompetency)
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_creation_basic(self):
        """Test creazione base competenza core"""
        safe_data = create_safe_data(
            CoreCompetency,
            name='Communication Skills'
        )
        
        competency = CoreCompetency(**safe_data)
        db.session.add(competency)
        db.session.commit()
        
        assert competency.id is not None
        assert competency.name == 'Communication Skills'
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_technical_skills(self):
        """Test competenze tecniche"""
        technical_competencies = [
            'Programming Languages',
            'Software Architecture',
            'Database Design',
            'System Administration',
            'Cloud Technologies',
            'DevOps Practices',
            'Security Best Practices',
            'Testing Methodologies'
        ]
        
        competencies = []
        for name in technical_competencies:
            safe_data = create_safe_data(
                CoreCompetency,
                name=name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica competenze tecniche
        for competency, expected_name in zip(competencies, technical_competencies):
            assert competency.name == expected_name
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_soft_skills(self):
        """Test competenze trasversali"""
        soft_competencies = [
            'Communication Skills',
            'Leadership Abilities',
            'Problem Solving',
            'Critical Thinking',
            'Teamwork',
            'Time Management',
            'Adaptability',
            'Emotional Intelligence'
        ]
        
        competencies = []
        for name in soft_competencies:
            safe_data = create_safe_data(
                CoreCompetency,
                name=name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica soft skills
        for competency, expected_name in zip(competencies, soft_competencies):
            assert competency.name == expected_name
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_business_skills(self):
        """Test competenze business"""
        business_competencies = [
            'Strategic Planning',
            'Project Management',
            'Financial Analysis',
            'Market Research',
            'Customer Relations',
            'Business Development',
            'Risk Management',
            'Quality Assurance'
        ]
        
        competencies = []
        for name in business_competencies:
            safe_data = create_safe_data(
                CoreCompetency,
                name=name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica business skills
        for competency, expected_name in zip(competencies, business_competencies):
            assert competency.name == expected_name
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_italian_names(self):
        """Test competenze con nomi italiani"""
        italian_competencies = [
            'Capacità Comunicative',
            'Leadership e Gestione Team',
            'Risoluzione Problemi',
            'Pensiero Critico',
            'Lavoro di Squadra',
            'Gestione del Tempo',
            'Adattabilità al Cambiamento',
            'Intelligenza Emotiva'
        ]
        
        competencies = []
        for name in italian_competencies:
            safe_data = create_safe_data(
                CoreCompetency,
                name=name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica nomi italiani
        for competency, expected_name in zip(competencies, italian_competencies):
            assert competency.name == expected_name
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_role_specific(self):
        """Test competenze specifiche per ruolo"""
        role_specific_competencies = [
            'Frontend Development',
            'Backend Architecture',
            'UI/UX Design Principles',
            'Data Analysis & Visualization',
            'Product Management',
            'Agile Methodologies',
            'Sales Techniques',
            'Marketing Strategy'
        ]
        
        competencies = []
        for name in role_specific_competencies:
            safe_data = create_safe_data(
                CoreCompetency,
                name=name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica competenze specifiche
        for competency, expected_name in zip(competencies, role_specific_competencies):
            assert competency.name == expected_name
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_duplicate_names(self):
        """Test competenze con nomi duplicati (permessi)"""
        duplicate_name = 'Communication Skills'
        
        competencies = []
        for i in range(3):
            safe_data = create_safe_data(
                CoreCompetency,
                name=duplicate_name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica duplicati permessi
        for competency in competencies:
            assert competency.id is not None
            assert competency.name == duplicate_name
        
        # Verifica ID diversi
        ids = [competency.id for competency in competencies]
        assert len(set(ids)) == 3
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_without_name(self):
        """Test competenza senza nome (permesso - campo nullable)"""
        safe_data = create_safe_data(CoreCompetency)
        
        competency = CoreCompetency(**safe_data)
        db.session.add(competency)
        db.session.commit()
        
        assert competency.id is not None
        assert competency.name is None
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_empty_name(self):
        """Test competenza con nome vuoto"""
        safe_data = create_safe_data(
            CoreCompetency,
            name=''
        )
        
        competency = CoreCompetency(**safe_data)
        db.session.add(competency)
        db.session.commit()
        
        assert competency.id is not None
        assert competency.name == ''
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_special_characters(self):
        """Test competenze con caratteri speciali"""
        special_competencies = [
            'C++ Programming',
            '.NET Development',
            'UX/UI Design',
            'AI & Machine Learning',
            'Cost-Benefit Analysis',
            'Team Building & Management',
            'Client Relations (B2B)',
            'Quality Assurance @ Scale'
        ]
        
        competencies = []
        for name in special_competencies:
            safe_data = create_safe_data(
                CoreCompetency,
                name=name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica caratteri speciali
        for competency, expected_name in zip(competencies, special_competencies):
            assert competency.name == expected_name
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_detailed_names(self):
        """Test competenze con nomi dettagliati"""
        detailed_competencies = [
            'Advanced Data Analysis and Statistical Modeling',
            'Cross-Functional Team Leadership and Coordination',
            'Client Relationship Management and Business Development',
            'Technical Documentation and Knowledge Transfer Skills'
        ]
        
        competencies = []
        for name in detailed_competencies:
            safe_data = create_safe_data(
                CoreCompetency,
                name=name
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica nomi dettagliati
        for competency, expected_name in zip(competencies, detailed_competencies):
            assert competency.name == expected_name
            assert len(competency.name) > 45
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            CoreCompetency,
            name='Test Competency Representation'
        )
        
        competency = CoreCompetency(**safe_data)
        db.session.add(competency)
        db.session.commit()
        
        repr_str = repr(competency)
        # Verifica rappresentazione
        assert 'CoreCompetency' in repr_str or 'Test Competency Representation' in repr_str
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_deletion(self):
        """Test eliminazione competenza"""
        safe_data = create_safe_data(
            CoreCompetency,
            name='Competency da Eliminare'
        )
        
        competency = CoreCompetency(**safe_data)
        db.session.add(competency)
        db.session.commit()
        competency_id = competency.id
        
        # Elimina
        db.session.delete(competency)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(CoreCompetency, competency_id)
        assert deleted is None
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_bulk_creation(self):
        """Test creazione massiva competenze"""
        competency_count = 12
        competencies = []
        
        for i in range(competency_count):
            safe_data = create_safe_data(
                CoreCompetency,
                name=f'Bulk Competency {i+1:02d}'
            )
            
            competency = CoreCompetency(**safe_data)
            competencies.append(competency)
            db.session.add(competency)
        
        db.session.commit()
        
        # Verifica creazione massiva
        assert len(competencies) == competency_count
        for i, competency in enumerate(competencies):
            assert competency.id is not None
            assert competency.name == f'Bulk Competency {i+1:02d}'
    
    @requires_db_consistency(CoreCompetency)
    def test_core_competency_minimal_data(self):
        """Test competenza con dati minimi"""
        safe_data = create_safe_data(CoreCompetency)
        
        competency = CoreCompetency(**safe_data)
        db.session.add(competency)
        db.session.commit()
        
        assert competency.id is not None
        assert competency.name is None