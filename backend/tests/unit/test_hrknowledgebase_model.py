"""Unit tests for HRKnowledgeBase model."""
import pytest
import json
from datetime import datetime
from models import HRKnowledgeBase, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestHRKnowledgeBaseModel:
    """Test per il modello HRKnowledgeBase (HR Assistant Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(HRKnowledgeBase)
    def test_table_exists(self):
        """Verifica che la tabella hr_knowledge_base esista"""
        assert_table_exists(HRKnowledgeBase)
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_creation_basic(self):
        """Test creazione base di un entry knowledge base HR"""
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='How to Request Annual Leave',
            content='To request annual leave, employees must submit a request through the HR portal at least 15 days in advance...',
            category='leave',
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        assert entry.id is not None
        assert entry.title == 'How to Request Annual Leave'
        assert entry.category == 'leave'
        assert entry.is_active is True  # Default value
        assert entry.created_with_ai is False  # Default value
        assert entry.created_by == self.user.id
        assert entry.created_at is not None
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_with_tags(self):
        """Test knowledge base entry con tags"""
        tags = ['vacation', 'time-off', 'policy', 'approval']
        tags_json = json.dumps(tags)
        
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='Vacation Request Process',
            content='Step-by-step guide for requesting vacation time...',
            category='leave',
            tags=tags_json,
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        assert entry.tags == tags_json
        # Test to_dict for tags parsing
        entry_dict = entry.to_dict()
        assert entry_dict['tags'] == tags
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_ai_assisted(self):
        """Test knowledge base entry creata con AI"""
        ai_sources = json.dumps([
            'https://hr-best-practices.com/onboarding',
            'https://employment-law.gov/guidelines',
            'internal-policy-documents'
        ])
        
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='Employee Onboarding Checklist',
            content='Complete onboarding checklist generated from best practices...',
            category='onboarding',
            created_with_ai=True,
            ai_sources=ai_sources,
            ai_confidence='high',
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        assert entry.created_with_ai is True
        assert entry.ai_sources == ai_sources
        assert entry.ai_confidence == 'high'
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_categories(self):
        """Test diverse categorie HR"""
        categories = [
            ('contracts', 'Employment Contract Guidelines'),
            ('onboarding', 'New Employee Onboarding Process'),
            ('offboarding', 'Employee Exit Procedures'),
            ('leave', 'Leave and Absence Policies'),
            ('permits', 'Work Permit Requirements'),
            ('travel', 'Business Travel Policy'),
            ('benefits', 'Employee Benefits Overview'),
            ('tools', 'IT Tools and Equipment'),
            ('purchases', 'Purchase Request Process'),
            ('training', 'Professional Development Programs')
        ]
        
        entries = []
        for category, title in categories:
            safe_data = create_safe_data(
                HRKnowledgeBase,
                title=title,
                content=f'Detailed information about {category} procedures...',
                category=category,
                created_by=self.user.id
            )
            
            entry = HRKnowledgeBase(**safe_data)
            entries.append(entry)
            db.session.add(entry)
        
        db.session.commit()
        
        # Verifica tutte le categorie
        for entry, (expected_category, expected_title) in zip(entries, categories):
            assert entry.category == expected_category
            assert entry.title == expected_title
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_ai_confidence_levels(self):
        """Test livelli di confidenza AI"""
        confidence_levels = ['high', 'medium', 'low']
        
        entries = []
        for confidence in confidence_levels:
            safe_data = create_safe_data(
                HRKnowledgeBase,
                title=f'AI Generated Content - {confidence.title()} Confidence',
                content=f'Content generated with {confidence} confidence level...',
                category='training',
                created_with_ai=True,
                ai_confidence=confidence,
                created_by=self.user.id
            )
            
            entry = HRKnowledgeBase(**safe_data)
            entries.append(entry)
            db.session.add(entry)
        
        db.session.commit()
        
        # Verifica tutti i livelli di confidenza
        for entry, expected_confidence in zip(entries, confidence_levels):
            assert entry.ai_confidence == expected_confidence
            assert entry.created_with_ai is True
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_active_status(self):
        """Test gestione stato attivo/inattivo"""
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='Active Status Test',
            content='Testing active status management...',
            category='training',
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        # Stato iniziale
        assert entry.is_active is True
        
        # Disattiva
        entry.is_active = False
        db.session.commit()
        assert entry.is_active is False
        
        # Riattiva
        entry.is_active = True
        db.session.commit()
        assert entry.is_active is True
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_user_relationship(self):
        """Test relazione con User (creator)"""
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='User Relationship Test',
            content='Testing user relationship...',
            category='training',
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        # Verifica relazione
        assert entry.creator is not None
        assert entry.creator.id == self.user.id
        assert entry.created_by == self.user.id
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_update_tracking(self):
        """Test tracking degli aggiornamenti"""
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='Update Tracking Test',
            content='Original content',
            category='training',
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        original_updated_at = entry.updated_at
        
        # Aggiorna il contenuto
        entry.content = 'Updated content with new information'
        entry.title = 'Updated Title'
        db.session.commit()
        
        # Verifica che updated_at sia cambiato
        assert entry.updated_at != original_updated_at
        assert entry.content == 'Updated content with new information'
        assert entry.title == 'Updated Title'
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_complex_content(self):
        """Test con contenuto complesso e lungo"""
        complex_content = """
        # Employee Onboarding Process
        
        ## Pre-boarding (1 week before start date)
        - Send welcome email with first-day instructions
        - Prepare workspace and equipment
        - Create accounts for all necessary systems
        
        ## First Day
        - Office tour and introductions
        - IT setup and security briefing
        - HR documentation completion
        
        ## First Week
        - Role-specific training sessions
        - Meet with direct manager and team
        - Complete mandatory compliance training
        
        ## First Month
        - Regular check-ins with HR and manager
        - Complete all required certifications
        - Begin role-specific responsibilities
        """
        
        tags = ['onboarding', 'checklist', 'process', 'new-hire', 'training']
        
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='Comprehensive Employee Onboarding Guide',
            content=complex_content,
            category='onboarding',
            tags=json.dumps(tags),
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        assert len(entry.content) > 500
        assert '# Employee Onboarding Process' in entry.content
        assert entry.category == 'onboarding'
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_to_dict(self):
        """Test serializzazione to_dict"""
        tags = ['policy', 'benefits', 'healthcare']
        ai_sources = ['hr-policy-docs', 'benefits-provider-website']
        
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='Employee Benefits Overview',
            content='Comprehensive overview of all employee benefits...',
            category='benefits',
            tags=json.dumps(tags),
            is_active=True,
            created_with_ai=True,
            ai_sources=json.dumps(ai_sources),
            ai_confidence='medium',
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        
        data = entry.to_dict()
        
        assert data['id'] == entry.id
        assert data['title'] == 'Employee Benefits Overview'
        assert data['content'] == 'Comprehensive overview of all employee benefits...'
        assert data['category'] == 'benefits'
        assert data['tags'] == tags
        assert data['is_active'] is True
        assert data['created_with_ai'] is True
        assert data['ai_confidence'] == 'medium'
        assert data['created_by'] == self.user.id
        assert 'created_at' in data
        assert 'updated_at' in data
        assert 'creator' in data
        assert data['creator']['id'] == self.user.id
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_validation(self):
        """Test validazione campi required"""
        # Test senza title (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                HRKnowledgeBase,
                content='Content without title',
                category='training',
                created_by=self.user.id
                # title mancante
            )
            entry = HRKnowledgeBase(**safe_data)
            db.session.add(entry)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza content (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                HRKnowledgeBase,
                title='Title without content',
                category='training',
                created_by=self.user.id
                # content mancante
            )
            entry = HRKnowledgeBase(**safe_data)
            db.session.add(entry)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza category (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                HRKnowledgeBase,
                title='Title without category',
                content='Content without category',
                created_by=self.user.id
                # category mancante
            )
            entry = HRKnowledgeBase(**safe_data)
            db.session.add(entry)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(HRKnowledgeBase)
    def test_hr_knowledge_base_deletion(self):
        """Test eliminazione entry knowledge base"""
        safe_data = create_safe_data(
            HRKnowledgeBase,
            title='To Delete Entry',
            content='This entry will be deleted',
            category='test',
            created_by=self.user.id
        )
        
        entry = HRKnowledgeBase(**safe_data)
        db.session.add(entry)
        db.session.commit()
        entry_id = entry.id
        
        # Elimina
        db.session.delete(entry)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(HRKnowledgeBase, entry_id)
        assert deleted is None