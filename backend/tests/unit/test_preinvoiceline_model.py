"""Unit tests for PreInvoiceLine model."""
import pytest
import json
from decimal import Decimal
from datetime import datetime
from models import PreInvoiceLine, PreInvoice, Project, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestPreInvoiceLineModel:
    """Test per il modello PreInvoiceLine (Invoicing Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user, test_client, test_project):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        self.client_id = test_client  # test_client è un ID
        self.project_id = test_project  # test_project è un ID
        
        # Crea una pre-fattura per i test delle righe
        with app.app_context():
            import time
            unique_number = f'PRE-TEST-{int(time.time() * 1000000)}'  # Usa microsecondi
            safe_data = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                pre_invoice_number=unique_number,
                billing_period_start=datetime.now().date().replace(day=1),
                billing_period_end=datetime.now().date(),
                created_by=self.user.id
            )
            
            pre_invoice = PreInvoice(**safe_data)
            db.session.add(pre_invoice)
            db.session.commit()
            self.pre_invoice_id = pre_invoice.id  # Salva solo l'ID
    
    @requires_table_exists(PreInvoiceLine)
    def test_table_exists(self):
        """Verifica che la tabella pre_invoice_lines esista"""
        assert_table_exists(PreInvoiceLine)
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_creation_basic(self):
        """Test creazione base di una riga pre-fattura"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Sviluppo modulo autenticazione',
            total_hours=Decimal('40.00'),
            hourly_rate=Decimal('75.00'),
            total_amount=Decimal('3000.00')
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.id is not None
        assert line.pre_invoice_id == self.pre_invoice_id
        assert line.project_id == self.project_id
        assert line.description == 'Sviluppo modulo autenticazione'
        assert line.total_hours == Decimal('40.00')
        assert line.hourly_rate == Decimal('75.00')
        assert line.total_amount == Decimal('3000.00')
        assert line.created_at is not None
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_with_timesheet_entries(self):
        """Test riga pre-fattura con timesheet entries collegati"""
        timesheet_ids = [123, 456, 789]
        
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Backend API development',
            total_hours=Decimal('80.5'),
            hourly_rate=Decimal('85.00'),
            total_amount=Decimal('6842.50'),
            timesheet_entries_ids=json.dumps(timesheet_ids)
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.timesheet_entries_ids == json.dumps(timesheet_ids)
        assert line.included_timesheet_ids == timesheet_ids
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_multiple_lines(self):
        """Test multiple righe per una pre-fattura"""
        line_data = [
            ('Frontend Development', 60.0, 70.0, 4200.0),
            ('Backend Development', 80.0, 85.0, 6800.0),
            ('Testing and QA', 25.0, 65.0, 1625.0),
            ('Documentation', 15.0, 50.0, 750.0)
        ]
        
        lines = []
        for description, hours, rate, amount in line_data:
            safe_data = create_safe_data(
                PreInvoiceLine,
                pre_invoice_id=self.pre_invoice_id,
                project_id=self.project_id,
                description=description,
                total_hours=Decimal(str(hours)),
                hourly_rate=Decimal(str(rate)),
                total_amount=Decimal(str(amount))
            )
            
            line = PreInvoiceLine(**safe_data)
            lines.append(line)
            db.session.add(line)
        
        db.session.commit()
        
        # Verifica tutte le righe
        for line, (expected_desc, expected_hours, expected_rate, expected_amount) in zip(lines, line_data):
            assert line.description == expected_desc
            assert line.total_hours == Decimal(str(expected_hours))
            assert line.hourly_rate == Decimal(str(expected_rate))
            assert line.total_amount == Decimal(str(expected_amount))
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_different_rates(self):
        """Test righe con diverse tariffe orarie"""
        rate_scenarios = [
            ('Junior Developer', 35.0, 50.0),
            ('Senior Developer', 40.0, 90.0),
            ('Tech Lead', 30.0, 120.0),
            ('Consultant', 20.0, 150.0)
        ]
        
        lines = []
        for role, hours, rate in rate_scenarios:
            total_amount = Decimal(str(hours)) * Decimal(str(rate))
            safe_data = create_safe_data(
                PreInvoiceLine,
                pre_invoice_id=self.pre_invoice_id,
                project_id=self.project_id,
                description=f'{role} services',
                total_hours=Decimal(str(hours)),
                hourly_rate=Decimal(str(rate)),
                total_amount=total_amount
            )
            
            line = PreInvoiceLine(**safe_data)
            lines.append(line)
            db.session.add(line)
        
        db.session.commit()
        
        # Verifica calcoli
        for line, (role, hours, rate) in zip(lines, rate_scenarios):
            expected_total = Decimal(str(hours)) * Decimal(str(rate))
            assert line.total_amount == expected_total
            assert line.hourly_rate == Decimal(str(rate))
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_timesheet_property(self):
        """Test property included_timesheet_ids getter/setter"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Property test line',
            total_hours=Decimal('10.0'),
            hourly_rate=Decimal('75.0'),
            total_amount=Decimal('750.0')
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        # Test setter e getter
        timesheet_ids = [100, 200, 300, 400]
        line.included_timesheet_ids = timesheet_ids
        
        assert line.included_timesheet_ids == timesheet_ids
        assert line.timesheet_entries_ids == json.dumps(timesheet_ids)
        
        # Test con lista vuota
        line.included_timesheet_ids = []
        assert line.included_timesheet_ids == []
        assert line.timesheet_entries_ids == json.dumps([])
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_no_timesheet_entries(self):
        """Test riga senza timesheet entries collegati"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Fixed price project milestone',
            total_hours=Decimal('0.0'),  # Fixed price, no hours tracked
            hourly_rate=Decimal('0.0'),
            total_amount=Decimal('5000.0')  # Fixed amount
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.timesheet_entries_ids is None
        assert line.included_timesheet_ids == []
        assert line.total_hours == Decimal('0.0')
        assert line.total_amount == Decimal('5000.0')
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_pre_invoice_relationship(self):
        """Test relazione con PreInvoice"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Relationship test line',
            total_hours=Decimal('5.0'),
            hourly_rate=Decimal('100.0'),
            total_amount=Decimal('500.0')
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        # Verifica relazione
        assert line.pre_invoice is not None
        assert line.pre_invoice.id == self.pre_invoice_id
        assert line.pre_invoice_id == self.pre_invoice_id
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_project_relationship(self):
        """Test relazione con Project"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Project relationship test',
            total_hours=Decimal('12.0'),
            hourly_rate=Decimal('80.0'),
            total_amount=Decimal('960.0')
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        # Verifica relazione
        assert line.project is not None
        assert line.project.id == self.project_id
        assert line.project_id == self.project_id
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_without_project(self):
        """Test riga senza progetto collegato (servizio generale)"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            description='General consulting services',
            total_hours=Decimal('8.0'),
            hourly_rate=Decimal('125.0'),
            total_amount=Decimal('1000.0')
            # project_id non specificato
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.project_id is None
        assert line.project is None
        assert line.description == 'General consulting services'
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_complex_descriptions(self):
        """Test con descrizioni complesse e dettagliate"""
        complex_descriptions = [
            'Implementation of user authentication system with OAuth2 integration and JWT tokens',
            'Database optimization and indexing for improved query performance (includes migration scripts)',
            'Frontend UI/UX redesign for mobile responsiveness and accessibility compliance',
            'CI/CD pipeline setup with automated testing, deployment, and monitoring integration'
        ]
        
        lines = []
        for i, description in enumerate(complex_descriptions):
            safe_data = create_safe_data(
                PreInvoiceLine,
                pre_invoice_id=self.pre_invoice_id,
                project_id=self.project_id,
                description=description,
                total_hours=Decimal(str(20 + i * 5)),
                hourly_rate=Decimal(str(75 + i * 10)),
                total_amount=Decimal(str((20 + i * 5) * (75 + i * 10)))
            )
            
            line = PreInvoiceLine(**safe_data)
            lines.append(line)
            db.session.add(line)
        
        db.session.commit()
        
        # Verifica descrizioni
        for line, expected_description in zip(lines, complex_descriptions):
            assert line.description == expected_description
            assert len(line.description) > 50  # Verifica che siano descrizioni dettagliate
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_large_timesheet_entries(self):
        """Test con molti timesheet entries collegati"""
        # Simula molti timesheet entries (es. un mese di lavoro)
        timesheet_ids = list(range(1000, 1150))  # 150 entries
        
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Monthly development work - June 2024',
            total_hours=Decimal('168.0'),  # ~1 mese di lavoro
            hourly_rate=Decimal('85.0'),
            total_amount=Decimal('14280.0'),
            timesheet_entries_ids=json.dumps(timesheet_ids)
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert len(line.included_timesheet_ids) == 150
        assert line.included_timesheet_ids[0] == 1000
        assert line.included_timesheet_ids[-1] == 1149
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Test representation',
            total_hours=Decimal('25.0'),
            hourly_rate=Decimal('90.0'),
            total_amount=Decimal('2250.0')
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        repr_str = repr(line)
        assert 'Test representation' in repr_str
        assert '25.0' in repr_str
        assert '90.0' in repr_str
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_validation(self):
        """Test validazione campi required"""
        # Test senza pre_invoice_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                PreInvoiceLine,
                project_id=self.project_id,
                description='Line without pre-invoice',
                total_hours=Decimal('10.0'),
                hourly_rate=Decimal('75.0'),
                total_amount=Decimal('750.0')
                # pre_invoice_id mancante
            )
            line = PreInvoiceLine(**safe_data)
            db.session.add(line)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza description (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                PreInvoiceLine,
                pre_invoice_id=self.pre_invoice_id,
                project_id=self.project_id,
                total_hours=Decimal('10.0'),
                hourly_rate=Decimal('75.0'),
                total_amount=Decimal('750.0')
                # description mancante
            )
            line = PreInvoiceLine(**safe_data)
            db.session.add(line)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_deletion(self):
        """Test eliminazione riga pre-fattura"""
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Line to delete',
            total_hours=Decimal('5.0'),
            hourly_rate=Decimal('100.0'),
            total_amount=Decimal('500.0')
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        line_id = line.id
        
        # Elimina
        db.session.delete(line)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(PreInvoiceLine, line_id)
        assert deleted is None
    
    @requires_db_consistency(PreInvoiceLine)
    def test_pre_invoice_line_cascade_deletion(self):
        """Test eliminazione in cascata con PreInvoice"""
        # Crea una riga collegata alla pre-fattura
        safe_data = create_safe_data(
            PreInvoiceLine,
            pre_invoice_id=self.pre_invoice_id,
            project_id=self.project_id,
            description='Cascade deletion test',
            total_hours=Decimal('10.0'),
            hourly_rate=Decimal('75.0'),
            total_amount=Decimal('750.0')
        )
        
        line = PreInvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        line_id = line.id
        pre_invoice_id = self.pre_invoice_id
        
        # Ottieni la pre-fattura dal database per eliminarla
        pre_invoice = db.session.get(PreInvoice, pre_invoice_id)
        db.session.delete(pre_invoice)
        db.session.commit()
        
        # Verifica che sia la pre-fattura che la riga siano eliminate
        deleted_pre_invoice = db.session.get(PreInvoice, pre_invoice_id)
        deleted_line = db.session.get(PreInvoiceLine, line_id)
        
        assert deleted_pre_invoice is None
        assert deleted_line is None