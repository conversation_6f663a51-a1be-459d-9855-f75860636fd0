"""Unit tests for CompanyProfile model."""
import pytest
from datetime import datetime
from models import CompanyProfile, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestCompanyProfileModel:
    """Test per il modello CompanyProfile (CEO AI Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(CompanyProfile)
    def test_table_exists(self):
        """Verifica che la tabella company_profiles esista"""
        assert_table_exists(CompanyProfile)
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_creation_basic(self):
        """Test creazione base di un profilo aziendale"""
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='TechStart Innovations',
            mission='Democratize AI technology for small businesses',
            industry='Technology',
            company_size='startup'
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        
        assert profile.id is not None
        assert profile.company_name == 'TechStart Innovations'
        assert profile.mission == 'Democratize AI technology for small businesses'
        assert profile.industry == 'Technology'
        assert profile.company_size == 'startup'
        assert profile.is_active is True  # Default value
        assert profile.created_at is not None
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_with_complete_data(self):
        """Test profilo aziendale con dati completi"""
        strategic_objectives = {
            'short_term': [
                'Acquire 100 new customers in Q2',
                'Launch mobile app',
                'Establish partnerships with 3 key vendors'
            ],
            'long_term': [
                'Become market leader in AI solutions for SMEs',
                'Expand to European markets',
                'IPO in 5 years'
            ]
        }
        
        analysis_focus_areas = [
            'customer_acquisition',
            'financial_performance',
            'product_development',
            'market_expansion'
        ]
        
        reporting_preferences = {
            'frequency': 'weekly',
            'format': 'executive_summary',
            'include_charts': True,
            'email_delivery': True
        }
        
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='Innovation Corp',
            mission='Transform industries through innovative technology solutions',
            vision='A world where technology empowers every business to thrive',
            values='Innovation, Integrity, Customer Success, Sustainability',
            industry='Software Technology',
            business_model='B2B',
            company_size='medium',
            target_market='Small and medium enterprises in Europe and North America',
            competitive_advantages='First-mover advantage in AI automation for SMEs',
            key_challenges='Market education, scaling customer success, talent acquisition',
            strategic_objectives=strategic_objectives,
            market_segment='AI Solutions for SMEs',
            geographic_focus='EU',
            revenue_model='SaaS subscription',
            current_stage='growth',
            analysis_focus_areas=analysis_focus_areas,
            reporting_preferences=reporting_preferences,
            updated_by=self.user.id
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        
        assert profile.company_name == 'Innovation Corp'
        assert profile.business_model == 'B2B'
        assert profile.strategic_objectives == strategic_objectives
        assert profile.analysis_focus_areas == analysis_focus_areas
        assert profile.reporting_preferences == reporting_preferences
        assert profile.updated_by == self.user.id
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_business_models(self):
        """Test diversi modelli di business"""
        business_models = [
            ('B2B', 'Enterprise Solutions'),
            ('B2C', 'Consumer Products'),
            ('B2B2C', 'Platform Business'),
            ('Marketplace', 'Digital Marketplace'),
            ('SaaS', 'Software as a Service')
        ]
        
        profiles = []
        for model, name in business_models:
            safe_data = create_safe_data(
                CompanyProfile,
                company_name=f'{name} Company',
                business_model=model,
                industry='Technology'
            )
            
            profile = CompanyProfile(**safe_data)
            profiles.append(profile)
            db.session.add(profile)
        
        db.session.commit()
        
        # Verifica tutti i modelli
        for profile, (expected_model, _) in zip(profiles, business_models):
            assert profile.business_model == expected_model
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_company_sizes(self):
        """Test diverse dimensioni aziendali"""
        company_sizes = [
            ('startup', 'TechStart'),
            ('small', 'SmallBiz Solutions'),
            ('medium', 'MediumTech Corp'),
            ('large', 'LargeCorp Enterprises')
        ]
        
        profiles = []
        for size, name in company_sizes:
            safe_data = create_safe_data(
                CompanyProfile,
                company_name=name,
                company_size=size,
                industry='Technology'
            )
            
            profile = CompanyProfile(**safe_data)
            profiles.append(profile)
            db.session.add(profile)
        
        db.session.commit()
        
        # Verifica tutte le dimensioni
        for profile, (expected_size, _) in zip(profiles, company_sizes):
            assert profile.company_size == expected_size
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_industries(self):
        """Test diversi settori industriali"""
        industries = [
            'Technology',
            'Healthcare',
            'Finance',
            'Manufacturing',
            'Retail',
            'Education',
            'Consulting'
        ]
        
        profiles = []
        for industry in industries:
            safe_data = create_safe_data(
                CompanyProfile,
                company_name=f'{industry} Company',
                industry=industry,
                company_size='medium'
            )
            
            profile = CompanyProfile(**safe_data)
            profiles.append(profile)
            db.session.add(profile)
        
        db.session.commit()
        
        # Verifica tutti i settori
        for profile, expected_industry in zip(profiles, industries):
            assert profile.industry == expected_industry
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_current_stages(self):
        """Test diversi stadi di sviluppo aziendale"""
        stages = [
            ('seed', 'Early stage startup'),
            ('startup', 'Growing startup'),
            ('growth', 'Scale-up company'),
            ('mature', 'Established business'),
            ('transformation', 'Digital transformation phase')
        ]
        
        profiles = []
        for stage, description in stages:
            safe_data = create_safe_data(
                CompanyProfile,
                company_name=f'Company in {stage} stage',
                current_stage=stage,
                industry='Technology',
                key_challenges=description
            )
            
            profile = CompanyProfile(**safe_data)
            profiles.append(profile)
            db.session.add(profile)
        
        db.session.commit()
        
        # Verifica tutti gli stadi
        for profile, (expected_stage, _) in zip(profiles, stages):
            assert profile.current_stage == expected_stage
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_geographic_focus(self):
        """Test diversi focus geografici"""
        geographic_focuses = [
            'Local',
            'Regional', 
            'National',
            'EU',
            'Global'
        ]
        
        profiles = []
        for focus in geographic_focuses:
            safe_data = create_safe_data(
                CompanyProfile,
                company_name=f'{focus} Business',
                geographic_focus=focus,
                industry='Technology'
            )
            
            profile = CompanyProfile(**safe_data)
            profiles.append(profile)
            db.session.add(profile)
        
        db.session.commit()
        
        # Verifica tutti i focus geografici
        for profile, expected_focus in zip(profiles, geographic_focuses):
            assert profile.geographic_focus == expected_focus
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_ai_preferences(self):
        """Test preferenze AI e analisi"""
        analysis_areas = [
            'financial_performance',
            'customer_analytics', 
            'operational_efficiency',
            'market_intelligence',
            'risk_management'
        ]
        
        reporting_prefs = {
            'executive_summary': True,
            'detailed_analytics': False,
            'visual_dashboards': True,
            'automated_alerts': True,
            'frequency': 'bi-weekly'
        }
        
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='AI-Focused Company',
            industry='Technology',
            analysis_focus_areas=analysis_areas,
            reporting_preferences=reporting_prefs
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        
        assert profile.analysis_focus_areas == analysis_areas
        assert profile.reporting_preferences == reporting_prefs
        assert len(profile.analysis_focus_areas) == 5
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_user_relationship(self):
        """Test relazione con User (updater)"""
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='User Relationship Test',
            industry='Technology',
            updated_by=self.user.id
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        
        # Verifica relazione
        assert profile.updater is not None
        assert profile.updater.id == self.user.id
        assert profile.updated_by == self.user.id
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_update_tracking(self):
        """Test tracking degli aggiornamenti"""
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='Update Tracking Test',
            mission='Original mission',
            industry='Technology',
            updated_by=self.user.id
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        
        original_updated_at = profile.updated_at
        
        # Aggiorna il profilo
        profile.mission = 'Updated mission statement'
        profile.vision = 'New vision added'
        db.session.commit()
        
        # Verifica che updated_at sia cambiato
        assert profile.updated_at != original_updated_at
        assert profile.mission == 'Updated mission statement'
        assert profile.vision == 'New vision added'
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_active_status(self):
        """Test gestione stato attivo"""
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='Active Status Test',
            industry='Technology'
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        
        # Stato iniziale
        assert profile.is_active is True
        
        # Disattiva
        profile.is_active = False
        db.session.commit()
        assert profile.is_active is False
        
        # Riattiva
        profile.is_active = True
        db.session.commit()
        assert profile.is_active is True
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_to_dict(self):
        """Test serializzazione to_dict"""
        strategic_objectives = {
            'short_term': ['Goal 1', 'Goal 2'],
            'long_term': ['Vision 1', 'Vision 2']
        }
        
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='Serialization Test Co',
            mission='Test mission for serialization',
            vision='Test vision for serialization',
            industry='Technology',
            company_size='startup',
            target_market='Tech startups and SMEs',
            competitive_advantages='Innovative AI solutions',
            strategic_objectives=strategic_objectives,
            market_segment='AI Tools'
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        
        data = profile.to_dict()
        
        assert data['id'] == profile.id
        assert data['company_name'] == 'Serialization Test Co'
        assert data['mission'] == 'Test mission for serialization'
        assert data['vision'] == 'Test vision for serialization'
        assert data['industry'] == 'Technology'
        assert data['company_size'] == 'startup'
        assert data['target_market'] == 'Tech startups and SMEs'
        assert data['competitive_advantages'] == 'Innovative AI solutions'
        assert data['strategic_objectives'] == strategic_objectives
        assert data['market_segment'] == 'AI Tools'
        assert 'updated_at' in data
    
    @requires_db_consistency(CompanyProfile)
    def test_company_profile_deletion(self):
        """Test eliminazione profilo aziendale"""
        safe_data = create_safe_data(
            CompanyProfile,
            company_name='To Delete Company',
            industry='Test',
            company_size='startup'
        )
        
        profile = CompanyProfile(**safe_data)
        db.session.add(profile)
        db.session.commit()
        profile_id = profile.id
        
        # Elimina
        db.session.delete(profile)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(CompanyProfile, profile_id)
        assert deleted is None