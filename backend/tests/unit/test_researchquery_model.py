"""Unit tests for ResearchQuery model."""
import pytest
from datetime import datetime
from models import ResearchQuery, ResearchSession, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestResearchQueryModel:
    """Test per il modello ResearchQuery (CEO AI Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        
        # Crea una research session per i test
        session_data = create_safe_data(
            ResearchSession,
            title='Test Session',
            category='market_analysis',
            user_id=self.user.id
        )
        self.session = ResearchSession(**session_data)
        db.session.add(self.session)
        db.session.commit()
    
    @requires_table_exists(ResearchQuery)
    def test_table_exists(self):
        """Verifica che la tabella research_queries esista"""
        assert_table_exists(ResearchQuery)
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_creation_basic(self):
        """Test creazione base di una research query"""
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='What are the latest market trends in AI?',
            query_type='market_trend'
        )
        
        query = ResearchQuery(**safe_data)
        db.session.add(query)
        db.session.commit()
        
        assert query.id is not None
        assert query.session_id == self.session.id
        assert query.query_text == 'What are the latest market trends in AI?'
        assert query.query_type == 'market_trend'
        assert query.status == 'pending'  # Default status
        assert query.started_at is not None
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_with_response(self):
        """Test query con response Perplexity completa"""
        perplexity_response = {
            'choices': [{
                'message': {
                    'content': 'AI market is growing rapidly...',
                    'role': 'assistant'
                }
            }],
            'usage': {
                'prompt_tokens': 50,
                'completion_tokens': 200,
                'total_tokens': 250
            }
        }
        
        processed_insights = [
            {
                'insight': 'AI market size will double by 2025',
                'confidence': 0.85,
                'source': 'market research'
            },
            {
                'insight': 'Computer vision is fastest growing segment',
                'confidence': 0.92,
                'source': 'industry reports'
            }
        ]
        
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='Analyze AI market growth trends',
            query_type='market_analysis',
            perplexity_response=perplexity_response,
            processed_insights=processed_insights,
            status='completed'
        )
        
        query = ResearchQuery(**safe_data)
        db.session.add(query)
        db.session.commit()
        
        assert query.perplexity_response == perplexity_response
        assert query.processed_insights == processed_insights
        assert query.status == 'completed'
        assert len(query.processed_insights) == 2
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_performance_metrics(self):
        """Test metriche di performance della query"""
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='Competitive analysis for fintech startups',
            query_type='competitor_analysis',
            response_time_seconds=2.5,
            token_count=1500,
            cost_estimate=0.0025
        )
        
        query = ResearchQuery(**safe_data)
        db.session.add(query)
        db.session.commit()
        
        assert query.response_time_seconds == 2.5
        assert query.token_count == 1500
        assert query.cost_estimate == 0.0025
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_status_workflow(self):
        """Test workflow degli stati della query"""
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='Investment opportunities in renewable energy',
            query_type='investment'
        )
        
        query = ResearchQuery(**safe_data)
        db.session.add(query)
        db.session.commit()
        
        # Stato iniziale
        assert query.status == 'pending'
        assert query.completed_at is None
        
        # In esecuzione
        query.status = 'running'
        db.session.commit()
        assert query.status == 'running'
        
        # Completata
        query.status = 'completed'
        query.completed_at = datetime.utcnow()
        db.session.commit()
        
        assert query.status == 'completed'
        assert query.completed_at is not None
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_error_handling(self):
        """Test gestione errori"""
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='Invalid query that failed',
            query_type='test',
            status='failed',
            error_message='API rate limit exceeded'
        )
        
        query = ResearchQuery(**safe_data)
        db.session.add(query)
        db.session.commit()
        
        assert query.status == 'failed'
        assert query.error_message == 'API rate limit exceeded'
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_session_relationship(self):
        """Test relazione con ResearchSession"""
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='Strategic planning analysis',
            query_type='strategic'
        )
        
        query = ResearchQuery(**safe_data)
        db.session.add(query)
        db.session.commit()
        
        # Verifica relazione
        assert query.session is not None
        assert query.session.id == self.session.id
        assert query.session.title == 'Test Session'
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_to_dict(self):
        """Test serializzazione to_dict"""
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='Market expansion opportunities',
            query_type='market_analysis',
            status='completed',
            response_time_seconds=1.8,
            processed_insights=[{'key': 'value'}]
        )
        
        query = ResearchQuery(**safe_data)
        query.completed_at = datetime.utcnow()
        db.session.add(query)
        db.session.commit()
        
        data = query.to_dict()
        
        assert data['id'] == query.id
        assert data['query_text'] == 'Market expansion opportunities'
        assert data['query_type'] == 'market_analysis'
        assert data['status'] == 'completed'
        assert 'started_at' in data
        assert 'completed_at' in data
        assert data['response_time'] == 1.8
        assert data['has_insights'] is True
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_types(self):
        """Test diversi tipi di query"""
        query_types = [
            'market_trend',
            'competitor_analysis',
            'investment',
            'talent',
            'technology_trends',
            'strategic'
        ]
        
        queries = []
        for i, query_type in enumerate(query_types):
            safe_data = create_safe_data(
                ResearchQuery,
                session_id=self.session.id,
                query_text=f'Query {i+1}: {query_type}',
                query_type=query_type
            )
            
            query = ResearchQuery(**safe_data)
            queries.append(query)
            db.session.add(query)
        
        db.session.commit()
        
        # Verifica che tutti i tipi siano stati salvati
        for query, expected_type in zip(queries, query_types):
            assert query.query_type == expected_type
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_validation(self):
        """Test validazione campi required"""
        # Test senza session_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                ResearchQuery,
                query_text='Invalid query',
                query_type='test'
                # session_id mancante
            )
            query = ResearchQuery(**safe_data)
            db.session.add(query)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza query_text (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                ResearchQuery,
                session_id=self.session.id,
                query_type='test'
                # query_text mancante
            )
            query = ResearchQuery(**safe_data)
            db.session.add(query)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(ResearchQuery)
    def test_research_query_deletion(self):
        """Test eliminazione query"""
        safe_data = create_safe_data(
            ResearchQuery,
            session_id=self.session.id,
            query_text='To delete',
            query_type='test'
        )
        
        query = ResearchQuery(**safe_data)
        db.session.add(query)
        db.session.commit()
        query_id = query.id
        
        # Elimina
        db.session.delete(query)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(ResearchQuery, query_id)
        assert deleted is None