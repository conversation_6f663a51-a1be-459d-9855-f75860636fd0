"""Unit tests for PerformanceKPI model."""
import pytest
from models import PerformanceKPI
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestPerformanceKPIModel:
    """Test per il modello PerformanceKPI (Performance Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app):
        """Setup per ogni test method"""
        self.app = app
    
    @requires_table_exists(PerformanceKPI)
    def test_table_exists(self):
        """Verifica che la tabella performance_kpis esista"""
        assert_table_exists(PerformanceKPI)
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_creation_basic(self):
        """Test creazione base KPI performance"""
        safe_data = create_safe_data(
            PerformanceKPI,
            name='Customer Satisfaction Score'
        )
        
        kpi = PerformanceKPI(**safe_data)
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.id is not None
        assert kpi.name == 'Customer Satisfaction Score'
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_different_types(self):
        """Test KPI con diversi tipi di metriche"""
        kpi_types = [
            'Customer Satisfaction Score',
            'Employee Engagement Rate',
            'Project Completion Rate',
            'Code Quality Score',
            'Time to Market',
            'Bug Resolution Time',
            'Revenue Growth',
            'Cost Reduction Percentage'
        ]
        
        kpis = []
        for kpi_name in kpi_types:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica tutti i KPI
        for kpi, expected_name in zip(kpis, kpi_types):
            assert kpi.name == expected_name
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_technical_metrics(self):
        """Test KPI tecnici per sviluppatori"""
        technical_kpis = [
            'Lines of Code per Day',
            'Code Coverage Percentage',
            'Cyclomatic Complexity',
            'Technical Debt Ratio',
            'API Response Time',
            'System Uptime',
            'Security Vulnerabilities Fixed',
            'Test Pass Rate'
        ]
        
        kpis = []
        for kpi_name in technical_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica KPI tecnici
        for kpi, expected_name in zip(kpis, technical_kpis):
            assert kpi.name == expected_name
            assert kpi.id is not None
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_business_metrics(self):
        """Test KPI business per manager"""
        business_kpis = [
            'Team Productivity Score',
            'Project Budget Adherence',
            'Client Retention Rate',
            'Team Turnover Rate',
            'Meeting Efficiency Score',
            'Stakeholder Satisfaction',
            'Resource Utilization Rate',
            'Innovation Index'
        ]
        
        kpis = []
        for kpi_name in business_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica KPI business
        for kpi, expected_name in zip(kpis, business_kpis):
            assert kpi.name == expected_name
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_quality_metrics(self):
        """Test KPI di qualità"""
        quality_kpis = [
            'Defect Density',
            'Customer Complaints Rate',
            'First Call Resolution',
            'Code Review Coverage',
            'Documentation Completeness',
            'Process Compliance Score',
            'User Experience Rating',
            'Performance Benchmark Score'
        ]
        
        kpis = []
        for kpi_name in quality_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica KPI qualità
        for kpi, expected_name in zip(kpis, quality_kpis):
            assert kpi.name == expected_name
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_long_names(self):
        """Test KPI con nomi dettagliati"""
        detailed_kpis = [
            'Average Time Between Software Releases to Production Environment',
            'Percentage of Critical Issues Resolved Within Service Level Agreement',
            'Customer Satisfaction Score Based on Post-Implementation Survey Results',
            'Team Collaboration Effectiveness Measured Through Cross-Functional Project Success'
        ]
        
        kpis = []
        for kpi_name in detailed_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica nomi dettagliati
        for kpi, expected_name in zip(kpis, detailed_kpis):
            assert kpi.name == expected_name
            assert len(kpi.name) > 50  # Nomi lunghi
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_short_names(self):
        """Test KPI con nomi brevi e acronimi"""
        short_kpis = [
            'ROI',
            'SLA',
            'KPI',
            'OKR',
            'NPS',
            'CSAT',
            'CRR',
            'TTV',
            'MTTR',
            'MTTF'
        ]
        
        kpis = []
        for kpi_name in short_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica acronimi
        for kpi, expected_name in zip(kpis, short_kpis):
            assert kpi.name == expected_name
            assert len(kpi.name) <= 5  # Nomi brevi
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_multilingual(self):
        """Test KPI con nomi in italiano"""
        italian_kpis = [
            'Punteggio Soddisfazione Cliente',
            'Tasso di Completamento Progetti',
            'Indice di Produttività Team',
            'Percentuale Rispetto Budget',
            'Tempo Medio Risoluzione Bug',
            'Livello Qualità Codice',
            'Efficienza Processo Sviluppo',
            'Indice Innovazione Prodotto'
        ]
        
        kpis = []
        for kpi_name in italian_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica nomi italiani
        for kpi, expected_name in zip(kpis, italian_kpis):
            assert kpi.name == expected_name
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_duplicate_names(self):
        """Test KPI con nomi duplicati (permessi)"""
        # Il modello non ha unique constraint, quindi duplicati dovrebbero essere permessi
        duplicate_name = 'Customer Satisfaction Score'
        
        kpis = []
        for i in range(3):
            safe_data = create_safe_data(
                PerformanceKPI,
                name=duplicate_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica che tutti i KPI duplicati siano stati creati
        for kpi in kpis:
            assert kpi.id is not None
            assert kpi.name == duplicate_name
        
        # Verifica che abbiano ID diversi
        ids = [kpi.id for kpi in kpis]
        assert len(set(ids)) == 3  # Tre ID diversi
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_without_name(self):
        """Test KPI senza nome (permesso - campo nullable)"""
        safe_data = create_safe_data(
            PerformanceKPI
            # name non specificato
        )
        
        kpi = PerformanceKPI(**safe_data)
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.id is not None
        assert kpi.name is None
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_empty_name(self):
        """Test KPI con nome vuoto"""
        safe_data = create_safe_data(
            PerformanceKPI,
            name=''
        )
        
        kpi = PerformanceKPI(**safe_data)
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.id is not None
        assert kpi.name == ''
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_whitespace_name(self):
        """Test KPI con nome solo spazi"""
        safe_data = create_safe_data(
            PerformanceKPI,
            name='   '
        )
        
        kpi = PerformanceKPI(**safe_data)
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.id is not None
        assert kpi.name == '   '
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_special_characters(self):
        """Test KPI con caratteri speciali"""
        special_kpis = [
            'Customer Satisfaction (NPS)',
            'ROI - Return on Investment',
            'Time-to-Market Score',
            'Quality@Scale Metric',
            'Team Performance #1',
            'Cost/Benefit Ratio',
            'Success Rate: 99.9%',
            'Innovation & Creativity Index'
        ]
        
        kpis = []
        for kpi_name in special_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica caratteri speciali
        for kpi, expected_name in zip(kpis, special_kpis):
            assert kpi.name == expected_name
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            PerformanceKPI,
            name='Test KPI Representation'
        )
        
        kpi = PerformanceKPI(**safe_data)
        db.session.add(kpi)
        db.session.commit()
        
        repr_str = repr(kpi)
        # Verifica che contenga almeno il nome della classe
        assert 'PerformanceKPI' in repr_str or 'Test KPI Representation' in repr_str
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_deletion(self):
        """Test eliminazione KPI"""
        safe_data = create_safe_data(
            PerformanceKPI,
            name='KPI da Eliminare'
        )
        
        kpi = PerformanceKPI(**safe_data)
        db.session.add(kpi)
        db.session.commit()
        kpi_id = kpi.id
        
        # Elimina
        db.session.delete(kpi)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(PerformanceKPI, kpi_id)
        assert deleted is None
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_bulk_creation(self):
        """Test creazione massiva KPI"""
        kpi_count = 20
        kpis = []
        
        for i in range(kpi_count):
            safe_data = create_safe_data(
                PerformanceKPI,
                name=f'Bulk KPI {i+1:02d}'
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append(kpi)
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica creazione massiva
        assert len(kpis) == kpi_count
        for i, kpi in enumerate(kpis):
            assert kpi.id is not None
            assert kpi.name == f'Bulk KPI {i+1:02d}'
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_complex_scenario(self):
        """Test scenario complesso con vari tipi di KPI"""
        complex_kpis = [
            ('Tech: Code Quality', 'technical'),
            ('Business: Revenue Growth', 'business'),
            ('Quality: Customer Satisfaction', 'quality'),
            ('Team: Collaboration Score', 'team'),
            ('Process: Efficiency Rate', 'process')
        ]
        
        kpis = []
        for kpi_name, category in complex_kpis:
            safe_data = create_safe_data(
                PerformanceKPI,
                name=kpi_name
            )
            
            kpi = PerformanceKPI(**safe_data)
            kpis.append((kpi, category))
            db.session.add(kpi)
        
        db.session.commit()
        
        # Verifica scenario complesso
        for (kpi, expected_category), (expected_name, _) in zip(kpis, complex_kpis):
            assert kpi.name == expected_name
            # Verifica che il nome contenga una parola chiave della categoria
            category_keywords = {
                'technical': ['tech'],
                'business': ['business'],
                'quality': ['quality'],
                'team': ['team'],
                'process': ['process']
            }
            keywords = category_keywords.get(expected_category, [expected_category])
            assert any(keyword in kpi.name.lower() for keyword in keywords)
    
    @requires_db_consistency(PerformanceKPI)
    def test_performance_kpi_minimal_data(self):
        """Test KPI con dati minimi"""
        # Crea KPI senza nome (campo nullable)
        safe_data = create_safe_data(PerformanceKPI)
        
        kpi = PerformanceKPI(**safe_data)
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.id is not None
        assert kpi.name is None