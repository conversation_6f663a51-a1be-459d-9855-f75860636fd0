"""Unit tests for HRChatConversation model."""
import pytest
import json
from datetime import datetime
from models import HRChatConversation, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestHRChatConversationModel:
    """Test per il modello HRChatConversation (HR Assistant Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(HRChatConversation)
    def test_table_exists(self):
        """Verifica che la tabella hr_chat_conversations esista"""
        assert_table_exists(HRChatConversation)
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_creation_basic(self):
        """Test creazione base di una conversazione HR chat"""
        safe_data = create_safe_data(
            HRChatConversation,
            user_id=self.user.id,
            session_id='session_001',
            user_message='How do I request vacation time?',
            bot_response='To request vacation time, you need to submit a request through the HR portal...'
        )
        
        conversation = HRChatConversation(**safe_data)
        db.session.add(conversation)
        db.session.commit()
        
        assert conversation.id is not None
        assert conversation.user_id == self.user.id
        assert conversation.session_id == 'session_001'
        assert conversation.user_message == 'How do I request vacation time?'
        assert conversation.bot_response.startswith('To request vacation time')
        assert conversation.created_at is not None
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_with_analytics(self):
        """Test conversazione con dati analytics completi"""
        kb_entries = [1, 5, 12]  # KB entry IDs used
        
        safe_data = create_safe_data(
            HRChatConversation,
            user_id=self.user.id,
            session_id='session_analytics_001',
            user_message='What are my benefits options?',
            bot_response='Your benefits include health insurance, dental coverage, 401k matching...',
            category_detected='benefits',
            confidence_score='high',
            kb_entries_used=json.dumps(kb_entries),
            response_time_ms=250,
            user_feedback='helpful'
        )
        
        conversation = HRChatConversation(**safe_data)
        db.session.add(conversation)
        db.session.commit()
        
        assert conversation.category_detected == 'benefits'
        assert conversation.confidence_score == 'high'
        assert conversation.kb_entries_used == json.dumps(kb_entries)
        assert conversation.response_time_ms == 250
        assert conversation.user_feedback == 'helpful'
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_categories(self):
        """Test diverse categorie rilevate automaticamente"""
        conversation_data = [
            ('leave', 'How many vacation days do I have?', 'You have 15 vacation days remaining...'),
            ('benefits', 'Tell me about health insurance', 'Our health insurance plan covers...'),
            ('onboarding', 'What do I need for my first day?', 'For your first day, please bring...'),
            ('training', 'Are there professional development opportunities?', 'Yes, we offer various training programs...'),
            ('tools', 'How do I access my work email?', 'To access your work email, follow these steps...'),
            ('travel', 'What is the travel reimbursement policy?', 'Our travel policy covers accommodation and meals...')
        ]
        
        conversations = []
        for category, user_msg, bot_resp in conversation_data:
            safe_data = create_safe_data(
                HRChatConversation,
                user_id=self.user.id,
                session_id=f'session_{category}',
                user_message=user_msg,
                bot_response=bot_resp,
                category_detected=category
            )
            
            conversation = HRChatConversation(**safe_data)
            conversations.append(conversation)
            db.session.add(conversation)
        
        db.session.commit()
        
        # Verifica tutte le categorie
        for conversation, (expected_category, _, _) in zip(conversations, conversation_data):
            assert conversation.category_detected == expected_category
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_confidence_levels(self):
        """Test livelli di confidenza AI"""
        confidence_levels = ['high', 'medium', 'low']
        
        conversations = []
        for confidence in confidence_levels:
            safe_data = create_safe_data(
                HRChatConversation,
                user_id=self.user.id,
                session_id=f'session_confidence_{confidence}',
                user_message=f'Question with {confidence} confidence response',
                bot_response=f'Response with {confidence} confidence level',
                confidence_score=confidence
            )
            
            conversation = HRChatConversation(**safe_data)
            conversations.append(conversation)
            db.session.add(conversation)
        
        db.session.commit()
        
        # Verifica tutti i livelli di confidenza
        for conversation, expected_confidence in zip(conversations, confidence_levels):
            assert conversation.confidence_score == expected_confidence
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_user_feedback(self):
        """Test feedback utente"""
        feedback_types = ['helpful', 'not_helpful', 'neutral']
        
        conversations = []
        for feedback in feedback_types:
            safe_data = create_safe_data(
                HRChatConversation,
                user_id=self.user.id,
                session_id=f'session_feedback_{feedback}',
                user_message='Test feedback question',
                bot_response='Test feedback response',
                user_feedback=feedback
            )
            
            conversation = HRChatConversation(**safe_data)
            conversations.append(conversation)
            db.session.add(conversation)
        
        db.session.commit()
        
        # Verifica tutti i tipi di feedback
        for conversation, expected_feedback in zip(conversations, feedback_types):
            assert conversation.user_feedback == expected_feedback
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_session_grouping(self):
        """Test raggruppamento per sessione"""
        session_id = 'multi_turn_session_001'
        
        messages = [
            ('Hello, I need help with HR questions', 'Hello! I\'m here to help with HR questions. What do you need?'),
            ('How do I update my address?', 'To update your address, log into the HR portal and go to Personal Information...'),
            ('What about emergency contacts?', 'Emergency contacts can be updated in the same Personal Information section...'),
            ('Thank you for the help!', 'You\'re welcome! Feel free to ask if you have more questions.')
        ]
        
        conversations = []
        for user_msg, bot_resp in messages:
            safe_data = create_safe_data(
                HRChatConversation,
                user_id=self.user.id,
                session_id=session_id,
                user_message=user_msg,
                bot_response=bot_resp
            )
            
            conversation = HRChatConversation(**safe_data)
            conversations.append(conversation)
            db.session.add(conversation)
        
        db.session.commit()
        
        # Verifica che tutte le conversazioni appartengano alla stessa sessione
        for conversation in conversations:
            assert conversation.session_id == session_id
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_response_time_tracking(self):
        """Test tracking tempi di risposta"""
        response_times = [100, 250, 500, 750, 1000]  # milliseconds
        
        conversations = []
        for i, response_time in enumerate(response_times):
            safe_data = create_safe_data(
                HRChatConversation,
                user_id=self.user.id,
                session_id=f'session_timing_{i}',
                user_message=f'Question {i+1}',
                bot_response=f'Response {i+1}',
                response_time_ms=response_time
            )
            
            conversation = HRChatConversation(**safe_data)
            conversations.append(conversation)
            db.session.add(conversation)
        
        db.session.commit()
        
        # Verifica tutti i tempi di risposta
        for conversation, expected_time in zip(conversations, response_times):
            assert conversation.response_time_ms == expected_time
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_user_relationship(self):
        """Test relazione con User"""
        safe_data = create_safe_data(
            HRChatConversation,
            user_id=self.user.id,
            session_id='relationship_test',
            user_message='Test user relationship',
            bot_response='Testing relationship...'
        )
        
        conversation = HRChatConversation(**safe_data)
        db.session.add(conversation)
        db.session.commit()
        
        # Verifica relazione
        assert conversation.user is not None
        assert conversation.user.id == self.user.id
        assert conversation.user_id == self.user.id
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_to_dict(self):
        """Test serializzazione to_dict"""
        kb_entries = [2, 7, 9]
        
        safe_data = create_safe_data(
            HRChatConversation,
            user_id=self.user.id,
            session_id='serialization_test',
            user_message='How do I submit a time-off request?',
            bot_response='To submit a time-off request, follow these steps...',
            category_detected='leave',
            confidence_score='high',
            kb_entries_used=json.dumps(kb_entries),
            response_time_ms=300,
            user_feedback='helpful'
        )
        
        conversation = HRChatConversation(**safe_data)
        db.session.add(conversation)
        db.session.commit()
        
        data = conversation.to_dict()
        
        assert data['id'] == conversation.id
        assert data['user_id'] == self.user.id
        assert data['session_id'] == 'serialization_test'
        assert data['user_message'] == 'How do I submit a time-off request?'
        assert data['category_detected'] == 'leave'
        assert data['confidence_score'] == 'high'
        assert data['kb_entries_used'] == kb_entries
        assert data['response_time_ms'] == 300
        assert data['user_feedback'] == 'helpful'
        assert 'created_at' in data
        assert 'user' in data
        assert data['user']['id'] == self.user.id
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_validation(self):
        """Test validazione campi required"""
        # Test senza user_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                HRChatConversation,
                session_id='invalid_session',
                user_message='Message without user',
                bot_response='Response without user'
                # user_id mancante
            )
            conversation = HRChatConversation(**safe_data)
            db.session.add(conversation)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza session_id (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                HRChatConversation,
                user_id=self.user.id,
                user_message='Message without session',
                bot_response='Response without session'
                # session_id mancante
            )
            conversation = HRChatConversation(**safe_data)
            db.session.add(conversation)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(HRChatConversation)
    def test_hr_chat_conversation_deletion(self):
        """Test eliminazione conversazione"""
        safe_data = create_safe_data(
            HRChatConversation,
            user_id=self.user.id,
            session_id='to_delete',
            user_message='Message to delete',
            bot_response='Response to delete'
        )
        
        conversation = HRChatConversation(**safe_data)
        db.session.add(conversation)
        db.session.commit()
        conversation_id = conversation.id
        
        # Elimina
        db.session.delete(conversation)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(HRChatConversation, conversation_id)
        assert deleted is None