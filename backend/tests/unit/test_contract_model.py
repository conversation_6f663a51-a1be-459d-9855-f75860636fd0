"""Unit tests for Contract model."""
import pytest
from datetime import date, datetime, timedelta
from models import Contract, Client, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestContractModel:
    """Test per il modello Contract (CRM Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user, test_client):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        self.client_id = test_client  # test_client è un ID
    
    @requires_table_exists(Contract)
    def test_table_exists(self):
        """Verifica che la tabella contracts esista"""
        assert_table_exists(Contract)
    
    @requires_db_consistency(Contract)
    def test_contract_creation_basic(self):
        """Test creazione base contratto"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-2024-001',
            title='Sviluppo Applicazione Web',
            description='<PERSON>tratto per sviluppo applicazione web custom',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=90)
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        assert contract.id is not None
        assert contract.client_id == self.client_id
        assert contract.contract_number == 'CTR-2024-001'
        assert contract.title == 'Sviluppo Applicazione Web'
        assert contract.contract_type == 'hourly'  # Default value
        assert contract.status == 'active'  # Default value
        assert contract.created_at is not None
    
    @requires_db_consistency(Contract)
    def test_contract_hourly_type(self):
        """Test contratto tipo hourly"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-HOURLY-001',
            title='Consulenza Tecnica Oraria',
            contract_type='hourly',
            hourly_rate=85.0,
            budget_hours=160.0,
            budget_amount=13600.0,
            start_date=date.today()
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        assert contract.contract_type == 'hourly'
        assert contract.hourly_rate == 85.0
        assert contract.budget_hours == 160.0
        assert contract.budget_amount == 13600.0
    
    @requires_db_consistency(Contract)
    def test_contract_fixed_type(self):
        """Test contratto tipo fixed price"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-FIXED-001',
            title='Progetto Prezzo Fisso',
            contract_type='fixed',
            budget_amount=25000.0,
            milestone_amount=5000.0,
            milestone_count=5,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=120)
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        assert contract.contract_type == 'fixed'
        assert contract.budget_amount == 25000.0
        assert contract.milestone_amount == 5000.0
        assert contract.milestone_count == 5
    
    @requires_db_consistency(Contract)
    def test_contract_retainer_type(self):
        """Test contratto tipo retainer"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-RETAINER-001',
            title='Supporto Mensile Retainer',
            contract_type='retainer',
            retainer_amount=3000.0,
            retainer_frequency='monthly',
            hourly_rate=90.0,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=365)
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        assert contract.contract_type == 'retainer'
        assert contract.retainer_amount == 3000.0
        assert contract.retainer_frequency == 'monthly'
        assert contract.hourly_rate == 90.0
    
    @requires_db_consistency(Contract)
    def test_contract_subscription_model(self):
        """Test contratto modello subscription"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-SUBSCRIPTION-001',
            title='Software as a Service',
            description='Contratto abbonamento mensile per servizio SaaS',
            contract_type='subscription',
            subscription_amount=299.0,
            subscription_frequency='monthly',
            start_date=date.today()
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        assert contract.subscription_amount == 299.0
        assert contract.subscription_frequency == 'monthly'
        assert contract.end_date is None  # Subscription può non avere fine
    
    @requires_db_consistency(Contract)
    def test_contract_status_workflow(self):
        """Test workflow stati contratto"""
        statuses = ['active', 'completed', 'cancelled']
        
        contracts = []
        for i, status in enumerate(statuses):
            safe_data = create_safe_data(
                Contract,
                client_id=self.client_id,
                contract_number=f'CTR-STATUS-{i+1:03d}',
                title=f'Contratto Status {status.title()}',
                start_date=date.today(),
                status=status
            )
            
            contract = Contract(**safe_data)
            contracts.append(contract)
            db.session.add(contract)
        
        db.session.commit()
        
        # Verifica tutti gli stati
        for contract, expected_status in zip(contracts, statuses):
            assert contract.status == expected_status
    
    @requires_db_consistency(Contract)
    def test_contract_date_ranges(self):
        """Test diverse durate contratti"""
        date_scenarios = [
            ('Short Term', 30),    # 1 mese
            ('Medium Term', 90),   # 3 mesi
            ('Long Term', 365),    # 1 anno
            ('Extended', 730)      # 2 anni
        ]
        
        contracts = []
        for i, (term_type, duration_days) in enumerate(date_scenarios):
            start_date = date.today() + timedelta(days=i)  # Stagger start dates
            end_date = start_date + timedelta(days=duration_days)
            
            safe_data = create_safe_data(
                Contract,
                client_id=self.client_id,
                contract_number=f'CTR-{term_type.upper().replace(" ", "")}-001',
                title=f'{term_type} Contract',
                start_date=start_date,
                end_date=end_date
            )
            
            contract = Contract(**safe_data)
            contracts.append(contract)
            db.session.add(contract)
        
        db.session.commit()
        
        # Verifica durate
        for contract, (term_type, expected_duration) in zip(contracts, date_scenarios):
            actual_duration = (contract.end_date - contract.start_date).days
            assert actual_duration == expected_duration
    
    @requires_db_consistency(Contract)
    def test_contract_different_rates(self):
        """Test diverse tariffe orarie"""
        rate_scenarios = [
            ('Junior Level', 45.0),
            ('Mid Level', 70.0),
            ('Senior Level', 95.0),
            ('Expert Level', 120.0),
            ('Consulting', 150.0)
        ]
        
        contracts = []
        for i, (level, rate) in enumerate(rate_scenarios):
            safe_data = create_safe_data(
                Contract,
                client_id=self.client_id,
                contract_number=f'CTR-RATE-{i+1:03d}',
                title=f'{level} Development Contract',
                contract_type='hourly',
                hourly_rate=rate,
                budget_hours=100.0,
                budget_amount=rate * 100,
                start_date=date.today()
            )
            
            contract = Contract(**safe_data)
            contracts.append(contract)
            db.session.add(contract)
        
        db.session.commit()
        
        # Verifica tariffe e calcoli
        for contract, (level, expected_rate) in zip(contracts, rate_scenarios):
            assert contract.hourly_rate == expected_rate
            assert contract.budget_amount == expected_rate * 100
    
    @requires_db_consistency(Contract)
    def test_contract_client_relationship(self):
        """Test relazione con Client"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-RELATIONSHIP-001',
            title='Test Client Relationship',
            start_date=date.today()
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        # Verifica relazione
        assert contract.client is not None
        assert contract.client.id == self.client_id
        assert contract.client_id == self.client_id
    
    @requires_db_consistency(Contract)
    def test_contract_unique_number(self):
        """Test vincolo unique su contract_number"""
        # Primo contratto
        safe_data_1 = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-UNIQUE-001',
            title='First Contract',
            start_date=date.today()
        )
        
        contract_1 = Contract(**safe_data_1)
        db.session.add(contract_1)
        db.session.commit()
        
        # Tentativo di duplicato (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data_2 = create_safe_data(
                Contract,
                client_id=self.client_id,
                contract_number='CTR-UNIQUE-001',  # Stesso numero
                title='Duplicate Contract',
                start_date=date.today()
            )
            contract_2 = Contract(**safe_data_2)
            db.session.add(contract_2)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(Contract)
    def test_contract_frequency_options(self):
        """Test opzioni di frequenza per retainer e subscription"""
        frequency_scenarios = [
            ('retainer', 'monthly'),
            ('retainer', 'quarterly'),
            ('retainer', 'yearly'),
            ('subscription', 'monthly'),
            ('subscription', 'yearly')
        ]
        
        contracts = []
        for i, (contract_type, frequency) in enumerate(frequency_scenarios):
            safe_data = create_safe_data(
                Contract,
                client_id=self.client_id,
                contract_number=f'CTR-FREQ-{i+1:03d}',
                title=f'{contract_type.title()} {frequency.title()} Contract',
                contract_type=contract_type,
                start_date=date.today()
            )
            
            if contract_type == 'retainer':
                safe_data['retainer_amount'] = 2000.0
                safe_data['retainer_frequency'] = frequency
            else:  # subscription
                safe_data['subscription_amount'] = 500.0
                safe_data['subscription_frequency'] = frequency
            
            contract = Contract(**safe_data)
            contracts.append(contract)
            db.session.add(contract)
        
        db.session.commit()
        
        # Verifica frequenze
        for contract, (expected_type, expected_frequency) in zip(contracts, frequency_scenarios):
            assert contract.contract_type == expected_type
            if expected_type == 'retainer':
                assert contract.retainer_frequency == expected_frequency
            else:
                assert contract.subscription_frequency == expected_frequency
    
    @requires_db_consistency(Contract)
    def test_contract_without_end_date(self):
        """Test contratto senza data fine (ongoing)"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-ONGOING-001',
            title='Ongoing Support Contract',
            contract_type='retainer',
            retainer_amount=1500.0,
            retainer_frequency='monthly',
            start_date=date.today()
            # end_date non specificato
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        assert contract.end_date is None
        assert contract.status == 'active'
    
    @requires_db_consistency(Contract)
    def test_contract_complex_pricing(self):
        """Test contratto con struttura prezzi complessa"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-COMPLEX-001',
            title='Complex Pricing Structure',
            description='Contratto con retainer base + ore extra + milestone',
            contract_type='retainer',
            hourly_rate=95.0,              # Tariffa ore extra
            retainer_amount=5000.0,        # Base mensile
            retainer_frequency='monthly',
            milestone_amount=10000.0,      # Bonus milestone
            milestone_count=3,
            budget_amount=50000.0,         # Budget totale
            start_date=date.today(),
            end_date=date.today() + timedelta(days=180)
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        assert contract.hourly_rate == 95.0
        assert contract.retainer_amount == 5000.0
        assert contract.milestone_amount == 10000.0
        assert contract.budget_amount == 50000.0
    
    @requires_db_consistency(Contract)
    def test_contract_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-REPR-001',
            title='Representation Test',
            start_date=date.today()
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        repr_str = repr(contract)
        assert 'CTR-REPR-001' in repr_str
        assert str(self.client_id) in repr_str
    
    @requires_db_consistency(Contract)
    def test_contract_validation(self):
        """Test validazione campi required"""
        # Test senza client_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                Contract,
                contract_number='CTR-INVALID-001',
                title='Contract without client',
                start_date=date.today()
                # client_id mancante
            )
            contract = Contract(**safe_data)
            db.session.add(contract)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza contract_number (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                Contract,
                client_id=self.client_id,
                title='Contract without number',
                start_date=date.today()
                # contract_number mancante
            )
            contract = Contract(**safe_data)
            db.session.add(contract)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(Contract)
    def test_contract_update_tracking(self):
        """Test tracking degli aggiornamenti"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-UPDATE-001',
            title='Update Tracking Test',
            start_date=date.today()
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        
        original_updated_at = contract.updated_at
        
        # Aggiorna alcuni campi
        contract.title = 'Updated Contract Title'
        contract.status = 'completed'
        contract.end_date = date.today() + timedelta(days=30)
        db.session.commit()
        
        # Verifica che updated_at sia cambiato
        assert contract.updated_at != original_updated_at
        assert contract.title == 'Updated Contract Title'
        assert contract.status == 'completed'
    
    @requires_db_consistency(Contract)
    def test_contract_deletion(self):
        """Test eliminazione contratto"""
        safe_data = create_safe_data(
            Contract,
            client_id=self.client_id,
            contract_number='CTR-DELETE-001',
            title='Contract to Delete',
            start_date=date.today()
        )
        
        contract = Contract(**safe_data)
        db.session.add(contract)
        db.session.commit()
        contract_id = contract.id
        
        # Elimina
        db.session.delete(contract)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(Contract, contract_id)
        assert deleted is None