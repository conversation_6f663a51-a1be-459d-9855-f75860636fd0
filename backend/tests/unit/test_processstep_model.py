"""Unit tests for ProcessStep model."""
import pytest
from models import ProcessStep, BusinessProcess, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestProcessStepModel:
    """Test per il modello ProcessStep (Business Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        
        # Crea un processo business per i test
        with app.app_context():
            import time
            unique_name = f'Test Process {int(time.time() * 1000000)}'
            safe_data = create_safe_data(
                BusinessProcess,
                name=unique_name,
                description='Test business process for steps'
            )
            
            process = BusinessProcess(**safe_data)
            db.session.add(process)
            db.session.commit()
            self.process_id = process.id
    
    @requires_table_exists(ProcessStep)
    def test_table_exists(self):
        """Verifica che la tabella process_steps esista"""
        assert_table_exists(ProcessStep)
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_creation_basic(self):
        """Test creazione base step processo"""
        safe_data = create_safe_data(
            ProcessStep,
            process_id=self.process_id,
            name='Verifica Documenti',
            description='Controllo e verifica di tutti i documenti necessari',
            order=1,
            estimated_duration=30
        )
        
        step = ProcessStep(**safe_data)
        db.session.add(step)
        db.session.commit()
        
        assert step.id is not None
        assert step.process_id == self.process_id
        assert step.name == 'Verifica Documenti'
        assert step.description == 'Controllo e verifica di tutti i documenti necessari'
        assert step.order == 1
        assert step.estimated_duration == 30
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_sequence(self):
        """Test sequenza step di processo"""
        steps_data = [
            ('Analisi Requisiti', 'Analisi e raccolta dei requisiti del progetto', 1, 120),
            ('Progettazione', 'Progettazione architetturale e tecnica', 2, 240),
            ('Implementazione', 'Sviluppo e implementazione della soluzione', 3, 480),
            ('Testing', 'Test e validazione della soluzione', 4, 180),
            ('Deployment', 'Rilascio in produzione', 5, 60)
        ]
        
        steps = []
        for name, desc, order, duration in steps_data:
            safe_data = create_safe_data(
                ProcessStep,
                process_id=self.process_id,
                name=name,
                description=desc,
                order=order,
                estimated_duration=duration
            )
            
            step = ProcessStep(**safe_data)
            steps.append(step)
            db.session.add(step)
        
        db.session.commit()
        
        # Verifica ordine e dati
        for step, (expected_name, expected_desc, expected_order, expected_duration) in zip(steps, steps_data):
            assert step.name == expected_name
            assert step.description == expected_desc
            assert step.order == expected_order
            assert step.estimated_duration == expected_duration
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_different_durations(self):
        """Test step con diverse durate"""
        duration_scenarios = [
            ('Quick Check', 5),      # 5 minuti
            ('Standard Review', 30), # 30 minuti
            ('Deep Analysis', 120),  # 2 ore
            ('Full Development', 480), # 8 ore
            ('Long Research', 960)   # 16 ore
        ]
        
        steps = []
        for i, (name, duration) in enumerate(duration_scenarios):
            safe_data = create_safe_data(
                ProcessStep,
                process_id=self.process_id,
                name=name,
                description=f'Step con durata di {duration} minuti',
                order=i + 1,
                estimated_duration=duration
            )
            
            step = ProcessStep(**safe_data)
            steps.append(step)
            db.session.add(step)
        
        db.session.commit()
        
        # Verifica durate
        for step, (expected_name, expected_duration) in zip(steps, duration_scenarios):
            assert step.name == expected_name
            assert step.estimated_duration == expected_duration
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_without_duration(self):
        """Test step senza durata stimata"""
        safe_data = create_safe_data(
            ProcessStep,
            process_id=self.process_id,
            name='Step Durata Variabile',
            description='Step con durata non determinabile',
            order=1
            # estimated_duration non specificato
        )
        
        step = ProcessStep(**safe_data)
        db.session.add(step)
        db.session.commit()
        
        assert step.estimated_duration is None
        assert step.name == 'Step Durata Variabile'
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_minimal_data(self):
        """Test step con dati minimi"""
        safe_data = create_safe_data(
            ProcessStep,
            process_id=self.process_id,
            name='Step Minimo'
            # Solo campi required
        )
        
        step = ProcessStep(**safe_data)
        db.session.add(step)
        db.session.commit()
        
        assert step.name == 'Step Minimo'
        assert step.description is None
        assert step.order == 0  # Default value
        assert step.estimated_duration is None
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_complex_descriptions(self):
        """Test step con descrizioni complesse"""
        complex_steps = [
            (
                'Analisi Architetturale Dettagliata',
                '''Eseguire un'analisi approfondita dell'architettura esistente includendo:
                - Valutazione delle performance attuali
                - Identificazione di bottleneck e criticità
                - Proposte di miglioramento e ottimizzazione
                - Documentazione tecnica completa'''
            ),
            (
                'Implementazione Sicurezza',
                '''Implementare tutte le misure di sicurezza necessarie:
                1. Configurazione SSL/TLS
                2. Implementazione autenticazione multi-fattore
                3. Configurazione firewall e monitoring
                4. Test di penetrazione e vulnerability assessment'''
            )
        ]
        
        steps = []
        for i, (name, description) in enumerate(complex_steps):
            safe_data = create_safe_data(
                ProcessStep,
                process_id=self.process_id,
                name=name,
                description=description,
                order=i + 1,
                estimated_duration=240  # 4 ore per step complessi
            )
            
            step = ProcessStep(**safe_data)
            steps.append(step)
            db.session.add(step)
        
        db.session.commit()
        
        # Verifica descrizioni dettagliate
        for step, (expected_name, expected_description) in zip(steps, complex_steps):
            assert step.name == expected_name
            assert step.description == expected_description
            assert len(step.description) > 100
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_ordering(self):
        """Test ordinamento step"""
        # Crea step con ordini non sequenziali
        order_scenarios = [10, 5, 15, 1, 8]
        
        steps = []
        for i, order in enumerate(order_scenarios):
            safe_data = create_safe_data(
                ProcessStep,
                process_id=self.process_id,
                name=f'Step Order {order}',
                order=order,
                estimated_duration=30
            )
            
            step = ProcessStep(**safe_data)
            steps.append(step)
            db.session.add(step)
        
        db.session.commit()
        
        # Verifica ordini
        for step, expected_order in zip(steps, order_scenarios):
            assert step.order == expected_order
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            ProcessStep,
            process_id=self.process_id,
            name='Test Representation Step'
        )
        
        step = ProcessStep(**safe_data)
        db.session.add(step)
        db.session.commit()
        
        repr_str = repr(step)
        assert 'Test Representation Step' in repr_str
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_validation(self):
        """Test validazione campi required"""
        # Test senza process_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                ProcessStep,
                name='Step senza processo'
                # process_id mancante
            )
            step = ProcessStep(**safe_data)
            db.session.add(step)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza name (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                ProcessStep,
                process_id=self.process_id
                # name mancante
            )
            step = ProcessStep(**safe_data)
            db.session.add(step)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(ProcessStep)
    def test_process_step_deletion(self):
        """Test eliminazione step processo"""
        safe_data = create_safe_data(
            ProcessStep,
            process_id=self.process_id,
            name='Step da Eliminare',
            description='Questo step sarà eliminato'
        )
        
        step = ProcessStep(**safe_data)
        db.session.add(step)
        db.session.commit()
        step_id = step.id
        
        # Elimina
        db.session.delete(step)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(ProcessStep, step_id)
        assert deleted is None