"""Unit tests for InvoiceLine model."""
import pytest
from datetime import datetime, date, timedelta
from models import InvoiceLine, Invoice, Project, Contract, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestInvoiceLineModel:
    """Test per il modello InvoiceLine (Invoicing Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user, test_client, test_project):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        self.client_id = test_client
        self.project_id = test_project
        
        # Crea una fattura per i test delle righe
        with app.app_context():
            import time
            unique_number = f'INV-TEST-{int(time.time() * 1000000)}'
            safe_data = create_safe_data(
                Invoice,
                client_id=self.client_id,
                invoice_number=unique_number,
                billing_period_start=datetime.now().date().replace(day=1),
                billing_period_end=datetime.now().date(),
                issue_date=datetime.now().date(),
                due_date=datetime.now().date() + timedelta(days=30)
            )
            
            invoice = Invoice(**safe_data)
            db.session.add(invoice)
            db.session.commit()
            self.invoice_id = invoice.id
    
    @requires_table_exists(InvoiceLine)
    def test_table_exists(self):
        """Verifica che la tabella invoice_lines esista"""
        assert_table_exists(InvoiceLine)
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_creation_basic(self):
        """Test creazione base riga fattura"""
        safe_data = create_safe_data(
            InvoiceLine,
            invoice_id=self.invoice_id,
            project_id=self.project_id,
            description='Sviluppo frontend applicazione',
            total_hours=40.0,
            hourly_rate=75.0,
            total_amount=3000.0
        )
        
        line = InvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.id is not None
        assert line.invoice_id == self.invoice_id
        assert line.project_id == self.project_id
        assert line.description == 'Sviluppo frontend applicazione'
        assert line.total_hours == 40.0
        assert line.hourly_rate == 75.0
        assert line.total_amount == 3000.0
        assert line.created_at is not None
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_with_contract(self):
        """Test riga fattura collegata a contratto"""
        # Crea un contratto per il test
        with self.app.app_context():
            import time
            unique_number = f'CTR-TEST-{int(time.time() * 1000000)}'
            safe_data = create_safe_data(
                Contract,
                client_id=self.client_id,
                contract_number=unique_number,
                title='Test Contract for Invoice Line',
                start_date=datetime.now().date()
            )
            
            contract = Contract(**safe_data)
            db.session.add(contract)
            db.session.commit()
            contract_id = contract.id
        
        safe_data = create_safe_data(
            InvoiceLine,
            invoice_id=self.invoice_id,
            contract_id=contract_id,
            description='Servizi contrattuali - Gennaio 2024',
            total_hours=80.0,
            hourly_rate=90.0,
            total_amount=7200.0
        )
        
        line = InvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.contract_id == contract_id
        assert line.project_id is None  # Collegato a contratto, non progetto
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_multiple_lines_same_invoice(self):
        """Test multiple righe per stessa fattura"""
        lines_data = [
            ('Analisi e progettazione', 20.0, 100.0, 2000.0),
            ('Sviluppo backend', 60.0, 85.0, 5100.0),
            ('Testing e QA', 15.0, 70.0, 1050.0),
            ('Documentazione', 10.0, 60.0, 600.0)
        ]
        
        lines = []
        for description, hours, rate, amount in lines_data:
            safe_data = create_safe_data(
                InvoiceLine,
                invoice_id=self.invoice_id,
                project_id=self.project_id,
                description=description,
                total_hours=hours,
                hourly_rate=rate,
                total_amount=amount
            )
            
            line = InvoiceLine(**safe_data)
            lines.append(line)
            db.session.add(line)
        
        db.session.commit()
        
        # Verifica tutte le righe appartengano alla stessa fattura
        for line in lines:
            assert line.invoice_id == self.invoice_id
        
        # Verifica dati specifici
        for line, (expected_desc, expected_hours, expected_rate, expected_amount) in zip(lines, lines_data):
            assert line.description == expected_desc
            assert line.total_hours == expected_hours
            assert line.hourly_rate == expected_rate
            assert line.total_amount == expected_amount
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_different_rates(self):
        """Test righe con diverse tariffe"""
        rate_scenarios = [
            ('Junior Developer', 35.0, 50.0),
            ('Senior Developer', 40.0, 90.0),
            ('Project Manager', 20.0, 120.0),
            ('Architect', 15.0, 150.0)
        ]
        
        lines = []
        for role, hours, rate in rate_scenarios:
            total_amount = hours * rate
            safe_data = create_safe_data(
                InvoiceLine,
                invoice_id=self.invoice_id,
                project_id=self.project_id,
                description=f'Servizi {role}',
                total_hours=hours,
                hourly_rate=rate,
                total_amount=total_amount
            )
            
            line = InvoiceLine(**safe_data)
            lines.append(line)
            db.session.add(line)
        
        db.session.commit()
        
        # Verifica calcoli corretti
        for line, (role, hours, rate) in zip(lines, rate_scenarios):
            expected_total = hours * rate
            assert line.total_amount == expected_total
            assert line.hourly_rate == rate
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_project_relationship(self):
        """Test relazione con Project"""
        safe_data = create_safe_data(
            InvoiceLine,
            invoice_id=self.invoice_id,
            project_id=self.project_id,
            description='Test project relationship',
            total_hours=25.0,
            hourly_rate=80.0,
            total_amount=2000.0
        )
        
        line = InvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        # Verifica relazione
        assert line.project is not None
        assert line.project.id == self.project_id
        assert line.project_id == self.project_id
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_without_project_or_contract(self):
        """Test riga senza progetto o contratto (servizio generale)"""
        safe_data = create_safe_data(
            InvoiceLine,
            invoice_id=self.invoice_id,
            description='Consulenza generale non collegata a progetti',
            total_hours=10.0,
            hourly_rate=100.0,
            total_amount=1000.0
            # project_id e contract_id non specificati
        )
        
        line = InvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.project_id is None
        assert line.contract_id is None
        assert line.project is None
        assert line.contract is None
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_long_descriptions(self):
        """Test righe con descrizioni dettagliate"""
        detailed_descriptions = [
            'Sviluppo completo del modulo di autenticazione utenti con implementazione OAuth2, JWT tokens, recupero password e gestione sessioni',
            'Ottimizzazione database con creazione indici, refactoring query complesse, implementazione caching Redis e monitoring performance',
            'Integrazione API esterne per pagamenti (Stripe, PayPal), gestione webhook, reconciliazione transazioni e reporting finanziario'
        ]
        
        lines = []
        for i, description in enumerate(detailed_descriptions):
            safe_data = create_safe_data(
                InvoiceLine,
                invoice_id=self.invoice_id,
                project_id=self.project_id,
                description=description,
                total_hours=50.0 + i * 10,
                hourly_rate=85.0,
                total_amount=(50.0 + i * 10) * 85.0
            )
            
            line = InvoiceLine(**safe_data)
            lines.append(line)
            db.session.add(line)
        
        db.session.commit()
        
        # Verifica descrizioni dettagliate
        for line, expected_description in zip(lines, detailed_descriptions):
            assert line.description == expected_description
            assert len(line.description) > 100
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_zero_hours_fixed_price(self):
        """Test riga a prezzo fisso (zero ore)"""
        safe_data = create_safe_data(
            InvoiceLine,
            invoice_id=self.invoice_id,
            project_id=self.project_id,
            description='Milestone completamento fase 1 - Prezzo fisso',
            total_hours=0.0,  # Prezzo fisso, non ore
            hourly_rate=0.0,
            total_amount=5000.0  # Importo fisso
        )
        
        line = InvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        assert line.total_hours == 0.0
        assert line.hourly_rate == 0.0
        assert line.total_amount == 5000.0
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            InvoiceLine,
            invoice_id=self.invoice_id,
            project_id=self.project_id,
            description='Test representation line',
            total_hours=30.0,
            hourly_rate=90.0,
            total_amount=2700.0
        )
        
        line = InvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        
        repr_str = repr(line)
        assert str(self.invoice_id) in repr_str
        assert 'Test representation line' in repr_str
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_validation(self):
        """Test validazione campi required"""
        # Test senza invoice_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                InvoiceLine,
                project_id=self.project_id,
                description='Line without invoice',
                total_hours=10.0,
                hourly_rate=75.0,
                total_amount=750.0
                # invoice_id mancante
            )
            line = InvoiceLine(**safe_data)
            db.session.add(line)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza description (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                InvoiceLine,
                invoice_id=self.invoice_id,
                project_id=self.project_id,
                total_hours=10.0,
                hourly_rate=75.0,
                total_amount=750.0
                # description mancante
            )
            line = InvoiceLine(**safe_data)
            db.session.add(line)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(InvoiceLine)
    def test_invoice_line_deletion(self):
        """Test eliminazione riga fattura"""
        safe_data = create_safe_data(
            InvoiceLine,
            invoice_id=self.invoice_id,
            project_id=self.project_id,
            description='Line to delete',
            total_hours=5.0,
            hourly_rate=100.0,
            total_amount=500.0
        )
        
        line = InvoiceLine(**safe_data)
        db.session.add(line)
        db.session.commit()
        line_id = line.id
        
        # Elimina
        db.session.delete(line)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(InvoiceLine, line_id)
        assert deleted is None