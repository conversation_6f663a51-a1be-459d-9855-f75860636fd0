"""Unit tests for HRAnalytics model."""
import pytest
import json
from datetime import date, datetime
from models import HRAnalytics
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestHRAnalyticsModel:
    """Test per il modello HRAnalytics (HR Assistant Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(HRAnalytics)
    def test_table_exists(self):
        """Verifica che la tabella hr_analytics esista"""
        assert_table_exists(HRAnalytics)
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_creation_basic(self):
        """Test creazione base di un record analytics HR"""
        today = date.today()
        
        safe_data = create_safe_data(
            HRAnalytics,
            date=today,
            total_conversations=25,
            unique_users=12,
            avg_response_time_ms=350.5
        )
        
        analytics = HRAnalytics(**safe_data)
        db.session.add(analytics)
        db.session.commit()
        
        assert analytics.id is not None
        assert analytics.date == today
        assert analytics.total_conversations == 25
        assert analytics.unique_users == 12
        assert analytics.avg_response_time_ms == 350.5
        assert analytics.created_at is not None
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_with_distributions(self):
        """Test analytics con distribuzioni categorie e feedback"""
        category_dist = {
            'onboarding': 8,
            'leave': 5,
            'benefits': 7,
            'training': 3,
            'tools': 2
        }
        
        feedback_dist = {
            'helpful': 18,
            'not_helpful': 4,
            'neutral': 3
        }
        
        safe_data = create_safe_data(
            HRAnalytics,
            date=date(2025, 6, 28),
            total_conversations=25,
            unique_users=15,
            avg_response_time_ms=280.0,
            category_distribution=json.dumps(category_dist),
            feedback_distribution=json.dumps(feedback_dist),
            total_kb_entries=120,
            ai_generated_entries=45
        )
        
        analytics = HRAnalytics(**safe_data)
        db.session.add(analytics)
        db.session.commit()
        
        assert analytics.category_distribution == json.dumps(category_dist)
        assert analytics.feedback_distribution == json.dumps(feedback_dist)
        assert analytics.total_kb_entries == 120
        assert analytics.ai_generated_entries == 45
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_multiple_days(self):
        """Test analytics per più giorni"""
        analytics_data = [
            (date(2025, 6, 25), 20, 10, 400.0),
            (date(2025, 6, 26), 35, 18, 350.0),
            (date(2025, 6, 27), 42, 22, 300.0),
            (date(2025, 6, 28), 38, 20, 325.0)
        ]
        
        analytics_records = []
        for analytics_date, conversations, users, response_time in analytics_data:
            safe_data = create_safe_data(
                HRAnalytics,
                date=analytics_date,
                total_conversations=conversations,
                unique_users=users,
                avg_response_time_ms=response_time
            )
            
            analytics = HRAnalytics(**safe_data)
            analytics_records.append(analytics)
            db.session.add(analytics)
        
        db.session.commit()
        
        # Verifica tutti i record
        for analytics, (expected_date, expected_conv, expected_users, expected_time) in zip(analytics_records, analytics_data):
            assert analytics.date == expected_date
            assert analytics.total_conversations == expected_conv
            assert analytics.unique_users == expected_users
            assert analytics.avg_response_time_ms == expected_time
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_category_distributions(self):
        """Test diverse distribuzioni di categorie"""
        category_distributions = [
            {'onboarding': 10, 'leave': 5, 'benefits': 3},
            {'training': 8, 'tools': 4, 'policies': 6},
            {'performance': 7, 'travel': 2, 'offboarding': 3}
        ]
        
        analytics_records = []
        for i, cat_dist in enumerate(category_distributions):
            safe_data = create_safe_data(
                HRAnalytics,
                date=date(2025, 6, 25 + i),
                total_conversations=sum(cat_dist.values()),
                unique_users=10 + i,
                category_distribution=json.dumps(cat_dist)
            )
            
            analytics = HRAnalytics(**safe_data)
            analytics_records.append(analytics)
            db.session.add(analytics)
        
        db.session.commit()
        
        # Verifica tutte le distribuzioni
        for analytics, expected_dist in zip(analytics_records, category_distributions):
            assert analytics.category_distribution == json.dumps(expected_dist)
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_feedback_tracking(self):
        """Test tracking feedback utenti"""
        feedback_scenarios = [
            {'helpful': 15, 'not_helpful': 2, 'neutral': 3},  # Mostly positive
            {'helpful': 8, 'not_helpful': 8, 'neutral': 4},   # Mixed feedback
            {'helpful': 5, 'not_helpful': 12, 'neutral': 3}   # Mostly negative
        ]
        
        analytics_records = []
        for i, feedback_dist in enumerate(feedback_scenarios):
            safe_data = create_safe_data(
                HRAnalytics,
                date=date(2025, 6, 20 + i),
                total_conversations=sum(feedback_dist.values()),
                unique_users=8 + i,
                feedback_distribution=json.dumps(feedback_dist)
            )
            
            analytics = HRAnalytics(**safe_data)
            analytics_records.append(analytics)
            db.session.add(analytics)
        
        db.session.commit()
        
        # Verifica tutti i feedback
        for analytics, expected_feedback in zip(analytics_records, feedback_scenarios):
            assert analytics.feedback_distribution == json.dumps(expected_feedback)
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_knowledge_base_stats(self):
        """Test statistiche knowledge base"""
        kb_stats = [
            (100, 30),  # 30% AI generated
            (150, 60),  # 40% AI generated
            (200, 120)  # 60% AI generated
        ]
        
        analytics_records = []
        for i, (total_entries, ai_entries) in enumerate(kb_stats):
            safe_data = create_safe_data(
                HRAnalytics,
                date=date(2025, 6, 15 + i),
                total_conversations=20 + i * 5,
                unique_users=10 + i,
                total_kb_entries=total_entries,
                ai_generated_entries=ai_entries
            )
            
            analytics = HRAnalytics(**safe_data)
            analytics_records.append(analytics)
            db.session.add(analytics)
        
        db.session.commit()
        
        # Verifica statistiche KB
        for analytics, (expected_total, expected_ai) in zip(analytics_records, kb_stats):
            assert analytics.total_kb_entries == expected_total
            assert analytics.ai_generated_entries == expected_ai
            # Verifica che AI entries non superino il totale
            assert analytics.ai_generated_entries <= analytics.total_kb_entries
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_performance_trends(self):
        """Test trend delle performance nel tempo"""
        # Simula miglioramento delle performance nel tempo
        performance_data = [
            (date(2025, 6, 20), 500.0, 15),  # Slow response, few users
            (date(2025, 6, 21), 450.0, 18),  # Improving
            (date(2025, 6, 22), 400.0, 22),  # Better
            (date(2025, 6, 23), 350.0, 25),  # Good
            (date(2025, 6, 24), 300.0, 30)   # Excellent
        ]
        
        analytics_records = []
        for analytics_date, response_time, users in performance_data:
            safe_data = create_safe_data(
                HRAnalytics,
                date=analytics_date,
                total_conversations=users * 2,  # Assume 2 conversations per user
                unique_users=users,
                avg_response_time_ms=response_time
            )
            
            analytics = HRAnalytics(**safe_data)
            analytics_records.append(analytics)
            db.session.add(analytics)
        
        db.session.commit()
        
        # Verifica trend di miglioramento
        for i in range(1, len(analytics_records)):
            current = analytics_records[i]
            previous = analytics_records[i-1]
            
            # Response time dovrebbe migliorare (diminuire)
            assert current.avg_response_time_ms <= previous.avg_response_time_ms
            # Users dovrebbero aumentare
            assert current.unique_users >= previous.unique_users
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_to_dict(self):
        """Test serializzazione to_dict"""
        category_dist = {'onboarding': 12, 'leave': 8, 'benefits': 5}
        feedback_dist = {'helpful': 20, 'not_helpful': 3, 'neutral': 2}
        analytics_date = date(2025, 6, 28)
        
        safe_data = create_safe_data(
            HRAnalytics,
            date=analytics_date,
            total_conversations=25,
            unique_users=15,
            avg_response_time_ms=275.5,
            category_distribution=json.dumps(category_dist),
            feedback_distribution=json.dumps(feedback_dist),
            total_kb_entries=135,
            ai_generated_entries=55
        )
        
        analytics = HRAnalytics(**safe_data)
        db.session.add(analytics)
        db.session.commit()
        
        data = analytics.to_dict()
        
        assert data['id'] == analytics.id
        assert data['date'] == analytics_date.isoformat()
        assert data['total_conversations'] == 25
        assert data['unique_users'] == 15
        assert data['avg_response_time_ms'] == 275.5
        assert data['category_distribution'] == category_dist
        assert data['feedback_distribution'] == feedback_dist
        assert data['total_kb_entries'] == 135
        assert data['ai_generated_entries'] == 55
        assert 'created_at' in data
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_validation(self):
        """Test validazione campi required"""
        # Test senza date (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                HRAnalytics,
                total_conversations=10,
                unique_users=5
                # date mancante
            )
            analytics = HRAnalytics(**safe_data)
            db.session.add(analytics)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_default_values(self):
        """Test valori di default"""
        today = date.today()
        
        safe_data = create_safe_data(
            HRAnalytics,
            date=today
            # Altri campi utilizzano valori di default
        )
        
        analytics = HRAnalytics(**safe_data)
        db.session.add(analytics)
        db.session.commit()
        
        assert analytics.total_conversations == 0
        assert analytics.unique_users == 0
        assert analytics.avg_response_time_ms == 0
        assert analytics.total_kb_entries == 0
        assert analytics.ai_generated_entries == 0
    
    @requires_db_consistency(HRAnalytics)
    def test_hr_analytics_deletion(self):
        """Test eliminazione record analytics"""
        safe_data = create_safe_data(
            HRAnalytics,
            date=date.today(),
            total_conversations=10,
            unique_users=5
        )
        
        analytics = HRAnalytics(**safe_data)
        db.session.add(analytics)
        db.session.commit()
        analytics_id = analytics.id
        
        # Elimina
        db.session.delete(analytics)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(HRAnalytics, analytics_id)
        assert deleted is None