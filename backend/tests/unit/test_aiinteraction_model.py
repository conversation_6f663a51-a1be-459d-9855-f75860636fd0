"""Unit tests for AIInteraction model."""
import pytest
from datetime import datetime
from models import AIInteraction, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestAIInteractionModel:
    """Test per il modello AIInteraction (CEO <PERSON> Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(AIInteraction)
    def test_table_exists(self):
        """Verifica che la tabella ai_interactions esista"""
        assert_table_exists(AIInteraction)
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_creation_basic(self):
        """Test creazione base di un'interazione AI"""
        safe_data = create_safe_data(
            AIInteraction,
            user_id=self.user.id,
            query='What are the key trends in our industry?',
            response='Based on current market analysis, the key trends include...'
        )
        
        interaction = AIInteraction(**safe_data)
        db.session.add(interaction)
        db.session.commit()
        
        assert interaction.id is not None
        assert interaction.user_id == self.user.id
        assert interaction.query == 'What are the key trends in our industry?'
        assert interaction.response == 'Based on current market analysis, the key trends include...'
        assert interaction.timestamp is not None
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_with_metadata(self):
        """Test interazione con metadati completi"""
        context_data = {
            'company_size': 'startup',
            'industry': 'technology',
            'revenue': '1M-5M',
            'employee_count': 25
        }
        
        safe_data = create_safe_data(
            AIInteraction,
            user_id=self.user.id,
            query='Analyze our competitive positioning',
            response='Your competitive positioning analysis shows...',
            conversation_id='conv_2025_001',
            category='strategic',
            context_data=context_data,
            model_used='gpt-4o',
            confidence_score=0.87
        )
        
        interaction = AIInteraction(**safe_data)
        db.session.add(interaction)
        db.session.commit()
        
        assert interaction.conversation_id == 'conv_2025_001'
        assert interaction.category == 'strategic'
        assert interaction.context_data == context_data
        assert interaction.model_used == 'gpt-4o'
        assert interaction.confidence_score == 0.87
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_performance_metrics(self):
        """Test metriche di performance dell'interazione"""
        safe_data = create_safe_data(
            AIInteraction,
            user_id=self.user.id,
            query='Generate market expansion strategy',
            response='Market expansion strategy recommendations: 1. European markets...',
            response_time_seconds=3.2,
            token_count_input=150,
            token_count_output=800,
            cost_estimate=0.0085
        )
        
        interaction = AIInteraction(**safe_data)
        db.session.add(interaction)
        db.session.commit()
        
        assert interaction.response_time_seconds == 3.2
        assert interaction.token_count_input == 150
        assert interaction.token_count_output == 800
        assert interaction.cost_estimate == 0.0085
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_user_feedback(self):
        """Test feedback utente sull'interazione"""
        safe_data = create_safe_data(
            AIInteraction,
            user_id=self.user.id,
            query='What are our biggest operational challenges?',
            response='Your biggest operational challenges appear to be...',
            user_rating=4,
            user_feedback='Very helpful analysis, but could use more specific metrics'
        )
        
        interaction = AIInteraction(**safe_data)
        db.session.add(interaction)
        db.session.commit()
        
        assert interaction.user_rating == 4
        assert interaction.user_feedback == 'Very helpful analysis, but could use more specific metrics'
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_categories(self):
        """Test diverse categorie di interazione"""
        categories = [
            ('strategic', 'Strategic planning advice'),
            ('operational', 'How to optimize our workflows?'),
            ('financial', 'Budget allocation recommendations'),
            ('marketing', 'Customer acquisition strategies'),
            ('hr', 'Team performance improvement')
        ]
        
        interactions = []
        for category, query in categories:
            safe_data = create_safe_data(
                AIInteraction,
                user_id=self.user.id,
                query=query,
                response=f'Response for {category} category...',
                category=category
            )
            
            interaction = AIInteraction(**safe_data)
            interactions.append(interaction)
            db.session.add(interaction)
        
        db.session.commit()
        
        # Verifica tutte le categorie
        for interaction, (expected_category, _) in zip(interactions, categories):
            assert interaction.category == expected_category
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_conversation_grouping(self):
        """Test raggruppamento conversazioni"""
        conversation_id = 'conv_strategic_2025'
        
        # Crea multiple interazioni nella stessa conversazione
        queries = [
            'What is our market position?',
            'How can we improve it?',
            'What are the implementation costs?'
        ]
        
        interactions = []
        for i, query in enumerate(queries):
            safe_data = create_safe_data(
                AIInteraction,
                user_id=self.user.id,
                query=query,
                response=f'Response {i+1} for strategic conversation',
                conversation_id=conversation_id,
                category='strategic'
            )
            
            interaction = AIInteraction(**safe_data)
            interactions.append(interaction)
            db.session.add(interaction)
        
        db.session.commit()
        
        # Verifica che tutte appartengano alla stessa conversazione
        for interaction in interactions:
            assert interaction.conversation_id == conversation_id
            assert interaction.category == 'strategic'
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_user_relationship(self):
        """Test relazione con User"""
        safe_data = create_safe_data(
            AIInteraction,
            user_id=self.user.id,
            query='User relationship test',
            response='Testing user relationship...'
        )
        
        interaction = AIInteraction(**safe_data)
        db.session.add(interaction)
        db.session.commit()
        
        # Verifica relazione
        assert interaction.user is not None
        assert interaction.user.id == self.user.id
        assert interaction.user_id == self.user.id
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_to_dict(self):
        """Test serializzazione to_dict"""
        safe_data = create_safe_data(
            AIInteraction,
            user_id=self.user.id,
            query='Serialization test query',
            response='This is the AI response for serialization test',
            conversation_id='test_conv_001',
            category='operational',
            confidence_score=0.91,
            user_rating=5
        )
        
        interaction = AIInteraction(**safe_data)
        db.session.add(interaction)
        db.session.commit()
        
        data = interaction.to_dict()
        
        assert data['id'] == interaction.id
        assert data['query'] == 'Serialization test query'
        assert data['response'] == 'This is the AI response for serialization test'
        assert data['conversation_id'] == 'test_conv_001'
        assert data['category'] == 'operational'
        assert data['confidence_score'] == 0.91
        assert data['user_rating'] == 5
        assert 'timestamp' in data
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_model_types(self):
        """Test diversi modelli AI utilizzati"""
        models = [
            ('gpt-4o', 'GPT-4 Optimized'),
            ('gpt-4o-mini', 'GPT-4 Mini'),
            ('claude-3-sonnet', 'Claude 3 Sonnet'),
            ('claude-3-haiku', 'Claude 3 Haiku')
        ]
        
        interactions = []
        for model_name, description in models:
            safe_data = create_safe_data(
                AIInteraction,
                user_id=self.user.id,
                query=f'Test query for {model_name}',
                response=f'Response generated by {description}',
                model_used=model_name
            )
            
            interaction = AIInteraction(**safe_data)
            interactions.append(interaction)
            db.session.add(interaction)
        
        db.session.commit()
        
        # Verifica tutti i modelli
        for interaction, (expected_model, _) in zip(interactions, models):
            assert interaction.model_used == expected_model
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_validation(self):
        """Test validazione campi required"""
        # Test senza user_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                AIInteraction,
                query='Query without user',
                response='Response without user'
                # user_id mancante
            )
            interaction = AIInteraction(**safe_data)
            db.session.add(interaction)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza query (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                AIInteraction,
                user_id=self.user.id,
                response='Response without query'
                # query mancante
            )
            interaction = AIInteraction(**safe_data)
            db.session.add(interaction)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza response (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                AIInteraction,
                user_id=self.user.id,
                query='Query without response'
                # response mancante
            )
            interaction = AIInteraction(**safe_data)
            db.session.add(interaction)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(AIInteraction)
    def test_ai_interaction_deletion(self):
        """Test eliminazione interazione"""
        safe_data = create_safe_data(
            AIInteraction,
            user_id=self.user.id,
            query='To delete',
            response='This interaction will be deleted'
        )
        
        interaction = AIInteraction(**safe_data)
        db.session.add(interaction)
        db.session.commit()
        interaction_id = interaction.id
        
        # Elimina
        db.session.delete(interaction)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(AIInteraction, interaction_id)
        assert deleted is None