"""Unit tests for CompanyInvoicingSettings model."""
import pytest
from decimal import Decimal
from datetime import datetime
from models import CompanyInvoicingSettings, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestCompanyInvoicingSettingsModel:
    """Test per il modello CompanyInvoicingSettings (Invoicing Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(CompanyInvoicingSettings)
    def test_table_exists(self):
        """Verifica che la tabella company_invoicing_settings esista"""
        assert_table_exists(CompanyInvoicingSettings)
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_creation_basic(self):
        """Test creazione base configurazione fatturazione aziendale"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='TechStart Solutions SRL',
            vat_number='*************',
            fiscal_code='****************',
            address='Via Roma 123, 20121 Milano MI',
            phone='+39 02 1234567',
            email='<EMAIL>',
            pec='<EMAIL>'
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        assert settings.id is not None
        assert settings.company_name == 'TechStart Solutions SRL'
        assert settings.vat_number == '*************'
        assert settings.fiscal_code == '****************'
        assert settings.default_vat_rate == Decimal('22.0')  # Default Italian VAT
        assert settings.default_retention_rate == Decimal('20.0')  # Default retention
        assert settings.is_active is True  # Default value
        assert settings.created_at is not None
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_with_custom_rates(self):
        """Test configurazione con tariffe personalizzate"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Consulting Pro SRL',
            vat_number='*************',
            default_vat_rate=Decimal('10.0'),  # Reduced VAT
            default_retention_rate=Decimal('0.0'),  # No retention
            default_payment_terms=60,  # 60 days payment
            tax_regime='forfettario'  # Flat tax regime
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        assert settings.default_vat_rate == Decimal('10.0')
        assert settings.default_retention_rate == Decimal('0.0')
        assert settings.default_payment_terms == 60
        assert settings.tax_regime == 'forfettario'
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_invoice_numbering(self):
        """Test configurazione numerazione fatture"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Innovative Software SRL',
            invoice_prefix='INV',
            current_year=2024,
            last_number=150
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        assert settings.invoice_prefix == 'INV'
        assert settings.current_year == 2024
        assert settings.last_number == 150
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_generate_next_number(self):
        """Test generazione numero fattura successivo"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Test Company SRL',
            invoice_prefix='TEST',
            current_year=2023,
            last_number=99
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        # Genera il prossimo numero
        next_number = settings.generate_next_number()
        
        # Verifica che il numero sia stato generato correttamente
        current_year = datetime.now().year
        expected_number = f"TEST-{current_year}-0001"
        assert next_number == expected_number
        
        # Verifica che i valori siano stati aggiornati
        assert settings.current_year == current_year
        assert settings.last_number == 1
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_generate_next_number_same_year(self):
        """Test generazione numero fattura stesso anno"""
        current_year = datetime.now().year
        
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Test Company SRL',
            invoice_prefix='SAMEYEAR',
            current_year=current_year,
            last_number=45
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        # Genera il prossimo numero (stesso anno)
        next_number = settings.generate_next_number()
        
        expected_number = f"SAMEYEAR-{current_year}-0046"
        assert next_number == expected_number
        assert settings.current_year == current_year
        assert settings.last_number == 46
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_different_tax_regimes(self):
        """Test diversi regimi fiscali"""
        tax_regimes = ['ordinario', 'semplificato', 'forfettario']
        
        settings_list = []
        for i, regime in enumerate(tax_regimes):
            safe_data = create_safe_data(
                CompanyInvoicingSettings,
                company_name=f'Company {regime.title()} SRL',
                vat_number=f'IT1234567890{i}',
                tax_regime=regime
            )
            
            settings = CompanyInvoicingSettings(**safe_data)
            settings_list.append(settings)
            db.session.add(settings)
        
        db.session.commit()
        
        # Verifica tutti i regimi
        for settings, expected_regime in zip(settings_list, tax_regimes):
            assert settings.tax_regime == expected_regime
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_complete_italian_company(self):
        """Test configurazione completa azienda italiana"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Innovazione Digitale SRL',
            vat_number='*************',
            fiscal_code='****************',
            address='Corso Italia 456, 10128 Torino TO',
            phone='+39 011 9876543',
            email='<EMAIL>',
            pec='<EMAIL>',
            default_vat_rate=Decimal('22.0'),
            default_retention_rate=Decimal('20.0'),
            default_payment_terms=30,
            invoice_prefix='INNDIG',
            current_year=2024,
            last_number=0,
            tax_regime='ordinario',
            is_active=True
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        assert settings.company_name == 'Innovazione Digitale SRL'
        assert settings.vat_number == '*************'
        assert settings.fiscal_code == '****************'
        assert 'Torino' in settings.address
        assert settings.pec == '<EMAIL>'
        assert settings.tax_regime == 'ordinario'
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_multiple_active_configs(self):
        """Test gestione multiple configurazioni attive/inattive"""
        # Configurazione attiva
        safe_data_active = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Active Company SRL',
            vat_number='*************',
            is_active=True
        )
        
        active_settings = CompanyInvoicingSettings(**safe_data_active)
        db.session.add(active_settings)
        
        # Configurazione inattiva
        safe_data_inactive = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Inactive Company SRL',
            vat_number='*************',
            is_active=False
        )
        
        inactive_settings = CompanyInvoicingSettings(**safe_data_inactive)
        db.session.add(inactive_settings)
        
        db.session.commit()
        
        assert active_settings.is_active is True
        assert inactive_settings.is_active is False
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_payment_terms_variations(self):
        """Test diverse condizioni di pagamento"""
        payment_terms = [15, 30, 45, 60, 90, 120]  # giorni
        
        settings_list = []
        for i, terms in enumerate(payment_terms):
            safe_data = create_safe_data(
                CompanyInvoicingSettings,
                company_name=f'Payment Terms {terms} SRL',
                vat_number=f'IT{str(i).zfill(11)}',
                default_payment_terms=terms
            )
            
            settings = CompanyInvoicingSettings(**safe_data)
            settings_list.append(settings)
            db.session.add(settings)
        
        db.session.commit()
        
        # Verifica tutte le condizioni di pagamento
        for settings, expected_terms in zip(settings_list, payment_terms):
            assert settings.default_payment_terms == expected_terms
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_vat_rates(self):
        """Test diverse aliquote IVA"""
        vat_scenarios = [
            ('Standard Rate', Decimal('22.0')),
            ('Reduced Rate', Decimal('10.0')),
            ('Super Reduced Rate', Decimal('4.0')),
            ('Zero Rate', Decimal('0.0'))
        ]
        
        settings_list = []
        for i, (scenario, rate) in enumerate(vat_scenarios):
            safe_data = create_safe_data(
                CompanyInvoicingSettings,
                company_name=f'{scenario} Company SRL',
                vat_number=f'IT{str(i+1000).zfill(8)}',
                default_vat_rate=rate
            )
            
            settings = CompanyInvoicingSettings(**safe_data)
            settings_list.append(settings)
            db.session.add(settings)
        
        db.session.commit()
        
        # Verifica tutte le aliquote
        for settings, (scenario, expected_rate) in zip(settings_list, vat_scenarios):
            assert settings.default_vat_rate == expected_rate
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_retention_rates(self):
        """Test diverse aliquote ritenuta"""
        retention_scenarios = [
            ('Standard Professional', Decimal('20.0')),
            ('Reduced Professional', Decimal('4.0')),
            ('No Retention', Decimal('0.0')),
            ('High Retention', Decimal('26.75'))
        ]
        
        settings_list = []
        for i, (scenario, rate) in enumerate(retention_scenarios):
            safe_data = create_safe_data(
                CompanyInvoicingSettings,
                company_name=f'{scenario} SRL',
                vat_number=f'IT{str(i+2000).zfill(8)}',
                default_retention_rate=rate
            )
            
            settings = CompanyInvoicingSettings(**safe_data)
            settings_list.append(settings)
            db.session.add(settings)
        
        db.session.commit()
        
        # Verifica tutte le ritenute
        for settings, (scenario, expected_rate) in zip(settings_list, retention_scenarios):
            assert settings.default_retention_rate == expected_rate
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_year_rollover(self):
        """Test rollover anno per numerazione fatture"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Year Rollover Test SRL',
            invoice_prefix='ROLLOVER',
            current_year=2023,
            last_number=999
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        # Simula cambio anno
        next_number = settings.generate_next_number()
        
        current_year = datetime.now().year
        expected_number = f"ROLLOVER-{current_year}-0001"
        assert next_number == expected_number
        assert settings.current_year == current_year
        assert settings.last_number == 1  # Reset to 1 for new year
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Repr Test SRL'
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        repr_str = repr(settings)
        assert 'Repr Test SRL' in repr_str
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_validation(self):
        """Test validazione campi required"""
        # Test senza company_name (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                CompanyInvoicingSettings,
                vat_number='*************',
                fiscal_code='****************'
                # company_name mancante
            )
            settings = CompanyInvoicingSettings(**safe_data)
            db.session.add(settings)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_update_tracking(self):
        """Test tracking degli aggiornamenti"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='Update Tracking Test SRL',
            vat_number='*************'
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        
        original_updated_at = settings.updated_at
        
        # Aggiorna qualche campo
        settings.company_name = 'Updated Company Name SRL'
        settings.default_payment_terms = 45
        db.session.commit()
        
        # Verifica che updated_at sia cambiato
        assert settings.updated_at != original_updated_at
        assert settings.company_name == 'Updated Company Name SRL'
        assert settings.default_payment_terms == 45
    
    @requires_db_consistency(CompanyInvoicingSettings)
    def test_company_invoicing_settings_deletion(self):
        """Test eliminazione configurazione fatturazione"""
        safe_data = create_safe_data(
            CompanyInvoicingSettings,
            company_name='To Delete Company SRL',
            vat_number='*************'
        )
        
        settings = CompanyInvoicingSettings(**safe_data)
        db.session.add(settings)
        db.session.commit()
        settings_id = settings.id
        
        # Elimina
        db.session.delete(settings)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(CompanyInvoicingSettings, settings_id)
        assert deleted is None