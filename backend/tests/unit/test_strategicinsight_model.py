"""Unit tests for StrategicInsight model."""
import pytest
from datetime import datetime
from models import StrategicInsight, ResearchSession, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestStrategicInsightModel:
    """Test per il modello StrategicInsight (CEO <PERSON> Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        
        # Crea una research session per i test
        session_data = create_safe_data(
            ResearchSession,
            title='Strategic Analysis Session',
            category='strategic',
            user_id=self.user.id
        )
        self.session = ResearchSession(**session_data)
        db.session.add(self.session)
        db.session.commit()
    
    @requires_table_exists(StrategicInsight)
    def test_table_exists(self):
        """Verifica che la tabella strategic_insights esista"""
        assert_table_exists(StrategicInsight)
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_creation_basic(self):
        """Test creazione base di un strategic insight"""
        safe_data = create_safe_data(
            StrategicInsight,
            session_id=self.session.id,
            insight_type='opportunity',
            title='AI Integration Opportunity',
            content='The company should consider integrating AI into customer service operations...'
        )
        
        insight = StrategicInsight(**safe_data)
        db.session.add(insight)
        db.session.commit()
        
        assert insight.id is not None
        assert insight.session_id == self.session.id
        assert insight.insight_type == 'opportunity'
        assert insight.title == 'AI Integration Opportunity'
        assert insight.priority == 'medium'  # Default value
        assert insight.status == 'new'  # Default value
        assert insight.created_at is not None
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_with_scoring(self):
        """Test insight con scoring e priorità"""
        action_items = [
            'Evaluate AI customer service platforms',
            'Pilot program with 100 customers',
            'Train support team on AI tools',
            'Measure customer satisfaction impact'
        ]
        
        tags = ['ai', 'customer_service', 'automation', 'efficiency']
        
        safe_data = create_safe_data(
            StrategicInsight,
            session_id=self.session.id,
            insight_type='recommendation',
            title='Implement AI-Powered Customer Support',
            content='Based on market analysis, AI customer support can reduce response time by 60%...',
            summary='AI customer support implementation recommendation',
            confidence_score=0.85,
            priority='high',
            impact_score=8,
            action_items=action_items,
            timeline_estimate='Q2 2025',
            tags=tags
        )
        
        insight = StrategicInsight(**safe_data)
        db.session.add(insight)
        db.session.commit()
        
        assert insight.confidence_score == 0.85
        assert insight.priority == 'high'
        assert insight.impact_score == 8
        assert insight.action_items == action_items
        assert insight.timeline_estimate == 'Q2 2025'
        assert insight.tags == tags
        assert len(insight.action_items) == 4
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_types(self):
        """Test diversi tipi di insight"""
        insight_types = [
            ('opportunity', 'Market Expansion Opportunity'),
            ('threat', 'Competitive Threat Analysis'),
            ('trend', 'Technology Trend Impact'),
            ('recommendation', 'Strategic Recommendation')
        ]
        
        insights = []
        for insight_type, title in insight_types:
            safe_data = create_safe_data(
                StrategicInsight,
                session_id=self.session.id,
                insight_type=insight_type,
                title=title,
                content=f'Content for {insight_type} insight...'
            )
            
            insight = StrategicInsight(**safe_data)
            insights.append(insight)
            db.session.add(insight)
        
        db.session.commit()
        
        # Verifica tutti i tipi
        for insight, (expected_type, expected_title) in zip(insights, insight_types):
            assert insight.insight_type == expected_type
            assert insight.title == expected_title
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_priorities(self):
        """Test diverse priorità"""
        priorities = ['low', 'medium', 'high', 'critical']
        
        insights = []
        for i, priority in enumerate(priorities):
            safe_data = create_safe_data(
                StrategicInsight,
                session_id=self.session.id,
                insight_type='opportunity',
                title=f'Priority Test {i+1}',
                content=f'Content for {priority} priority insight',
                priority=priority,
                impact_score=i+3  # 3-6 range
            )
            
            insight = StrategicInsight(**safe_data)
            insights.append(insight)
            db.session.add(insight)
        
        db.session.commit()
        
        for insight, expected_priority in zip(insights, priorities):
            assert insight.priority == expected_priority
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_review_workflow(self):
        """Test workflow di review degli insight"""
        safe_data = create_safe_data(
            StrategicInsight,
            session_id=self.session.id,
            insight_type='recommendation',
            title='Cost Reduction Strategy',
            content='Implement automated processes to reduce operational costs...'
        )
        
        insight = StrategicInsight(**safe_data)
        db.session.add(insight)
        db.session.commit()
        
        # Stato iniziale
        assert insight.status == 'new'
        assert insight.reviewed_by is None
        assert insight.reviewed_at is None
        
        # Review
        insight.status = 'reviewed'
        insight.reviewed_by = self.user.id
        insight.reviewed_at = datetime.utcnow()
        insight.notes = 'Approved for implementation in Q3'
        db.session.commit()
        
        assert insight.status == 'reviewed'
        assert insight.reviewed_by == self.user.id
        assert insight.reviewed_at is not None
        assert insight.notes == 'Approved for implementation in Q3'
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_source_tracking(self):
        """Test tracking delle query sorgenti"""
        source_queries = [101, 102, 105]  # IDs delle query che hanno generato l'insight
        
        safe_data = create_safe_data(
            StrategicInsight,
            session_id=self.session.id,
            insight_type='trend',
            title='Emerging Technology Trend',
            content='Analysis shows increasing adoption of edge computing...',
            source_queries=source_queries,
            confidence_score=0.92
        )
        
        insight = StrategicInsight(**safe_data)
        db.session.add(insight)
        db.session.commit()
        
        assert insight.source_queries == source_queries
        assert len(insight.source_queries) == 3
        assert 101 in insight.source_queries
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_session_relationship(self):
        """Test relazione con ResearchSession"""
        safe_data = create_safe_data(
            StrategicInsight,
            session_id=self.session.id,
            insight_type='opportunity',
            title='Strategic Partnership Opportunity',
            content='Partnership with tech companies could accelerate growth...'
        )
        
        insight = StrategicInsight(**safe_data)
        db.session.add(insight)
        db.session.commit()
        
        # Verifica relazione
        assert insight.session is not None
        assert insight.session.id == self.session.id
        assert insight.session.title == 'Strategic Analysis Session'
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_to_dict(self):
        """Test serializzazione to_dict"""
        action_items = ['Action 1', 'Action 2']
        tags = ['strategy', 'growth']
        
        safe_data = create_safe_data(
            StrategicInsight,
            session_id=self.session.id,
            insight_type='opportunity',
            title='Market Entry Strategy',
            content='Analysis of European market entry opportunities...',
            summary='European expansion recommended',
            confidence_score=0.78,
            priority='high',
            impact_score=9,
            action_items=action_items,
            timeline_estimate='6 months',
            tags=tags,
            status='reviewed'
        )
        
        insight = StrategicInsight(**safe_data)
        insight.reviewed_at = datetime.utcnow()
        db.session.add(insight)
        db.session.commit()
        
        data = insight.to_dict()
        
        assert data['id'] == insight.id
        assert data['insight_type'] == 'opportunity'
        assert data['title'] == 'Market Entry Strategy'
        assert data['content'] == 'Analysis of European market entry opportunities...'
        assert data['summary'] == 'European expansion recommended'
        assert data['confidence_score'] == 0.78
        assert data['priority'] == 'high'
        assert data['impact_score'] == 9
        assert data['action_items'] == action_items
        assert data['timeline_estimate'] == '6 months'
        assert data['status'] == 'reviewed'
        assert data['tags'] == tags
        assert 'created_at' in data
        assert 'reviewed_at' in data
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_validation(self):
        """Test validazione campi required"""
        # Test senza session_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                StrategicInsight,
                insight_type='opportunity',
                title='Invalid Insight',
                content='Content without session'
                # session_id mancante
            )
            insight = StrategicInsight(**safe_data)
            db.session.add(insight)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza title (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                StrategicInsight,
                session_id=self.session.id,
                insight_type='opportunity',
                content='Content without title'
                # title mancante
            )
            insight = StrategicInsight(**safe_data)
            db.session.add(insight)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza content (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                StrategicInsight,
                session_id=self.session.id,
                insight_type='opportunity',
                title='Title without content'
                # content mancante
            )
            insight = StrategicInsight(**safe_data)
            db.session.add(insight)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(StrategicInsight)
    def test_strategic_insight_deletion(self):
        """Test eliminazione insight"""
        safe_data = create_safe_data(
            StrategicInsight,
            session_id=self.session.id,
            insight_type='test',
            title='To Delete',
            content='This insight will be deleted'
        )
        
        insight = StrategicInsight(**safe_data)
        db.session.add(insight)
        db.session.commit()
        insight_id = insight.id
        
        # Elimina
        db.session.delete(insight)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(StrategicInsight, insight_id)
        assert deleted is None