"""Unit tests for IntegrationSettings model."""
import pytest
import json
from datetime import datetime
from models import IntegrationSettings, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestIntegrationSettingsModel:
    """Test per il modello IntegrationSettings (Invoicing Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(IntegrationSettings)
    def test_table_exists(self):
        """Verifica che la tabella integration_settings esista"""
        assert_table_exists(IntegrationSettings)
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_creation_basic(self):
        """Test creazione base integrazione"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='fattureincloud',
            config_data='{"api_key": "test_key", "company_id": "12345"}',
            is_active=True,
            sync_status='ok'
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        assert integration.id is not None
        assert integration.service_name == 'fattureincloud'
        assert integration.is_active is True
        assert integration.sync_status == 'ok'
        assert integration.created_at is not None
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_fattureincloud(self):
        """Test configurazione FattureInCloud"""
        config_data = {
            "api_key": "fc_api_key_12345",
            "company_id": "67890",
            "base_url": "https://api-v2.fattureincloud.it",
            "webhook_url": "https://myapp.com/webhooks/fattureincloud"
        }
        
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='fattureincloud',
            config_data=json.dumps(config_data),
            is_active=True,
            sync_status='ok',
            sync_message='Connected successfully'
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        assert integration.service_name == 'fattureincloud'
        parsed_config = integration.get_config()
        assert parsed_config['api_key'] == 'fc_api_key_12345'
        assert parsed_config['company_id'] == '67890'
        assert 'fattureincloud.it' in parsed_config['base_url']
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_contabilita_semplice(self):
        """Test configurazione Contabilità Semplice"""
        config_data = {
            "api_token": "cs_token_abcdef",
            "user_id": "user123",
            "endpoint": "https://api.contabilitasemplice.it",
            "auto_sync": True
        }
        
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='contabilita_semplice',
            config_data=json.dumps(config_data),
            is_active=True,
            sync_status='ok'
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        assert integration.service_name == 'contabilita_semplice'
        parsed_config = integration.get_config()
        assert parsed_config['api_token'] == 'cs_token_abcdef'
        assert parsed_config['auto_sync'] is True
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_multiple_services(self):
        """Test configurazione servizi multipli"""
        services_config = [
            ('fattureincloud', {'api_key': 'fc_key', 'company_id': '111'}),
            ('aruba_fatturazione', {'username': 'aruba_user', 'password': 'aruba_pass'}),
            ('teamleader', {'api_token': 'tl_token', 'subdomain': 'mycompany'}),
            ('hubspot', {'api_key': 'hs_key', 'portal_id': '12345'}),
            ('zapier', {'webhook_url': 'https://hooks.zapier.com/hooks/catch/123/abc'})
        ]
        
        integrations = []
        for service_name, config in services_config:
            safe_data = create_safe_data(
                IntegrationSettings,
                service_name=service_name,
                config_data=json.dumps(config),
                is_active=True,
                sync_status='ok'
            )
            
            integration = IntegrationSettings(**safe_data)
            integrations.append(integration)
            db.session.add(integration)
        
        db.session.commit()
        
        # Verifica tutti i servizi
        for integration, (expected_service, expected_config) in zip(integrations, services_config):
            assert integration.service_name == expected_service
            assert integration.get_config() == expected_config
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_sync_statuses(self):
        """Test diversi stati di sincronizzazione"""
        sync_scenarios = [
            ('ok', 'Synchronization successful'),
            ('error', 'Connection timeout after 30 seconds'),
            ('warning', 'Some records could not be synchronized'),
            ('pending', 'Synchronization in progress'),
            ('disabled', 'Service temporarily disabled')
        ]
        
        integrations = []
        for i, (status, message) in enumerate(sync_scenarios):
            safe_data = create_safe_data(
                IntegrationSettings,
                service_name=f'test_service_{i}',
                config_data='{"test": "config"}',
                sync_status=status,
                sync_message=message
            )
            
            integration = IntegrationSettings(**safe_data)
            integrations.append(integration)
            db.session.add(integration)
        
        db.session.commit()
        
        # Verifica tutti gli stati
        for integration, (expected_status, expected_message) in zip(integrations, sync_scenarios):
            assert integration.sync_status == expected_status
            assert integration.sync_message == expected_message
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_field_mappings(self):
        """Test mapping campi personalizzati"""
        field_mappings = {
            "local_client_name": "customer_name",
            "local_vat_number": "vat_id",
            "local_address": "billing_address",
            "local_payment_terms": "payment_conditions",
            "local_invoice_number": "document_number",
            "local_issue_date": "invoice_date"
        }
        
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='custom_erp',
            config_data='{"api_endpoint": "https://erp.example.com/api"}',
            field_mappings=json.dumps(field_mappings),
            is_active=True
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        assert integration.field_mappings == json.dumps(field_mappings)
        # Test that we could parse mappings if needed
        parsed_mappings = json.loads(integration.field_mappings)
        assert parsed_mappings['local_client_name'] == 'customer_name'
        assert len(parsed_mappings) == 6
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_sync_tracking(self):
        """Test tracking delle sincronizzazioni"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='sync_tracker',
            config_data='{"test": "config"}',
            is_active=True
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        # Stato iniziale
        assert integration.last_sync is None
        assert integration.sync_status == 'ok'  # Default
        
        # Simula sincronizzazione
        sync_time = datetime.utcnow()
        integration.last_sync = sync_time
        integration.sync_status = 'ok'
        integration.sync_message = 'Successfully synced 15 invoices'
        db.session.commit()
        
        assert integration.last_sync == sync_time
        assert integration.sync_message == 'Successfully synced 15 invoices'
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_active_inactive(self):
        """Test gestione stato attivo/inattivo"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='toggle_test',
            config_data='{"api_key": "test"}',
            is_active=True
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        # Stato iniziale
        assert integration.is_active is True
        
        # Disattiva
        integration.is_active = False
        integration.sync_status = 'disabled'
        integration.sync_message = 'Service disabled by user'
        db.session.commit()
        
        assert integration.is_active is False
        assert integration.sync_status == 'disabled'
        
        # Riattiva
        integration.is_active = True
        integration.sync_status = 'ok'
        integration.sync_message = 'Service reactivated'
        db.session.commit()
        
        assert integration.is_active is True
        assert integration.sync_status == 'ok'
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_get_config_method(self):
        """Test metodo get_config()"""
        config_data = {
            "api_key": "secret_key",
            "timeout": 30,
            "retry_attempts": 3,
            "debug_mode": False
        }
        
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='config_test',
            config_data=json.dumps(config_data)
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        # Test get_config
        parsed_config = integration.get_config()
        assert parsed_config == config_data
        assert parsed_config['timeout'] == 30
        assert parsed_config['debug_mode'] is False
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_set_config_method(self):
        """Test metodo set_config()"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='set_config_test'
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        # Test set_config
        new_config = {
            "api_url": "https://api.example.com",
            "version": "v2",
            "features": ["invoicing", "contacts", "products"]
        }
        
        integration.set_config(new_config)
        
        assert integration.config_data == json.dumps(new_config)
        assert integration.get_config() == new_config
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_empty_config(self):
        """Test integrazione senza configurazione"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='empty_config_test',
            config_data=None
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        # get_config dovrebbe restituire dict vuoto
        parsed_config = integration.get_config()
        assert parsed_config == {}
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_complex_config(self):
        """Test configurazione complessa con strutture nidificate"""
        complex_config = {
            "authentication": {
                "type": "oauth2",
                "client_id": "my_client_id",
                "client_secret": "my_client_secret",
                "scope": ["read", "write", "admin"]
            },
            "endpoints": {
                "invoices": "https://api.service.com/v2/invoices",
                "customers": "https://api.service.com/v2/customers",
                "products": "https://api.service.com/v2/products"
            },
            "settings": {
                "auto_sync": True,
                "sync_interval_minutes": 15,
                "batch_size": 100,
                "error_handling": {
                    "retry_attempts": 3,
                    "backoff_strategy": "exponential"
                }
            }
        }
        
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='complex_service',
            config_data=json.dumps(complex_config)
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        parsed_config = integration.get_config()
        assert parsed_config['authentication']['type'] == 'oauth2'
        assert len(parsed_config['authentication']['scope']) == 3
        assert parsed_config['settings']['batch_size'] == 100
        assert parsed_config['settings']['error_handling']['retry_attempts'] == 3
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='repr_test',
            is_active=True
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        repr_str = repr(integration)
        assert 'repr_test' in repr_str
        assert 'Active' in repr_str
        
        # Test con servizio inattivo
        integration.is_active = False
        db.session.commit()
        
        repr_str_inactive = repr(integration)
        assert 'Inactive' in repr_str_inactive
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_validation(self):
        """Test validazione campi required"""
        # Test senza service_name (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                IntegrationSettings,
                config_data='{"test": "config"}',
                is_active=True
                # service_name mancante
            )
            integration = IntegrationSettings(**safe_data)
            db.session.add(integration)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_update_tracking(self):
        """Test tracking degli aggiornamenti"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='update_test',
            config_data='{"initial": "config"}'
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        
        original_updated_at = integration.updated_at
        
        # Aggiorna configurazione
        new_config = {"updated": "config", "version": 2}
        integration.set_config(new_config)
        integration.sync_status = 'ok'
        db.session.commit()
        
        # Verifica che updated_at sia cambiato
        assert integration.updated_at != original_updated_at
        assert integration.get_config() == new_config
    
    @requires_db_consistency(IntegrationSettings)
    def test_integration_settings_deletion(self):
        """Test eliminazione integrazione"""
        safe_data = create_safe_data(
            IntegrationSettings,
            service_name='to_delete',
            config_data='{"temp": "config"}'
        )
        
        integration = IntegrationSettings(**safe_data)
        db.session.add(integration)
        db.session.commit()
        integration_id = integration.id
        
        # Elimina
        db.session.delete(integration)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(IntegrationSettings, integration_id)
        assert deleted is None