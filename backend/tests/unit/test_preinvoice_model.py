"""Unit tests for PreInvoice model."""
import pytest
from decimal import Decimal
from datetime import date, datetime
from models import PreInvoice, Client, Contract, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestPreInvoiceModel:
    """Test per il modello PreInvoice (Invoicing Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user, test_client):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
        self.client_id = test_client  # test_client è un ID, non un oggetto
    
    @requires_table_exists(PreInvoice)
    def test_table_exists(self):
        """Verifica che la tabella pre_invoices esista"""
        assert_table_exists(PreInvoice)
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_creation_basic(self):
        """Test creazione base di una pre-fattura"""
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-2024-0001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        
        assert pre_invoice.id is not None
        assert pre_invoice.client_id == self.client_id
        assert pre_invoice.pre_invoice_number == 'PRE-2024-0001'
        assert pre_invoice.status == 'draft'  # Default value
        assert pre_invoice.vat_rate == 22.0  # Default Italian VAT
        assert pre_invoice.created_at is not None
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_with_financial_data(self):
        """Test pre-fattura con dati finanziari completi"""
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-2024-0002',
            billing_period_start=date(2024, 7, 1),
            billing_period_end=date(2024, 7, 31),
            subtotal=Decimal('1000.00'),
            vat_rate=Decimal('22.0'),
            vat_amount=Decimal('220.00'),
            retention_rate=Decimal('20.0'),
            retention_amount=Decimal('200.00'),
            total_amount=Decimal('1020.00'),
            status='ready',
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        
        assert pre_invoice.subtotal == Decimal('1000.00')
        assert pre_invoice.vat_rate == Decimal('22.0')
        assert pre_invoice.vat_amount == Decimal('220.00')
        assert pre_invoice.retention_rate == Decimal('20.0')
        assert pre_invoice.retention_amount == Decimal('200.00')
        assert pre_invoice.total_amount == Decimal('1020.00')
        assert pre_invoice.status == 'ready'
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_status_workflow(self):
        """Test workflow stati pre-fattura"""
        statuses = ['draft', 'ready', 'sent_external', 'invoiced']
        
        pre_invoices = []
        for i, status in enumerate(statuses):
            safe_data = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                pre_invoice_number=f'PRE-2024-{i+10:04d}',
                billing_period_start=date(2024, 6 + i, 1),
                billing_period_end=date(2024, 6 + i, 30),
                status=status,
                created_by=self.user.id
            )
            
            pre_invoice = PreInvoice(**safe_data)
            pre_invoices.append(pre_invoice)
            db.session.add(pre_invoice)
        
        db.session.commit()
        
        # Verifica tutti gli stati
        for pre_invoice, expected_status in zip(pre_invoices, statuses):
            assert pre_invoice.status == expected_status
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_external_integration(self):
        """Test integrazione con sistemi esterni"""
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-2024-EXT001',
            billing_period_start=date(2024, 8, 1),
            billing_period_end=date(2024, 8, 31),
            status='sent_external',
            external_invoice_id='FC_INV_123456',
            external_status='sent',
            external_pdf_url='https://api.fattureincloud.it/invoices/123456.pdf',
            external_sent_date=date(2024, 9, 1),
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        
        assert pre_invoice.external_invoice_id == 'FC_INV_123456'
        assert pre_invoice.external_status == 'sent'
        assert pre_invoice.external_pdf_url.startswith('https://api.fattureincloud.it')
        assert pre_invoice.external_sent_date == date(2024, 9, 1)
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_display_status(self):
        """Test property display_status"""
        statuses_mapping = [
            ('draft', 'Bozza'),
            ('ready', 'Pronta'),
            ('sent_external', 'Inviata a Sistema Esterno'),
            ('invoiced', 'Fatturata')
        ]
        
        for status, expected_display in statuses_mapping:
            safe_data = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                pre_invoice_number=f'PRE-TEST-{status.upper()}',
                billing_period_start=date(2024, 6, 1),
                billing_period_end=date(2024, 6, 30),
                status=status,
                created_by=self.user.id
            )
            
            pre_invoice = PreInvoice(**safe_data)
            db.session.add(pre_invoice)
            db.session.commit()
            
            assert pre_invoice.display_status == expected_display
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_can_edit_property(self):
        """Test property can_edit"""
        editable_statuses = ['draft', 'ready']
        non_editable_statuses = ['sent_external', 'invoiced']
        
        # Test stati modificabili
        for status in editable_statuses:
            safe_data = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                pre_invoice_number=f'PRE-EDIT-{status.upper()}',
                billing_period_start=date(2024, 6, 1),
                billing_period_end=date(2024, 6, 30),
                status=status,
                created_by=self.user.id
            )
            
            pre_invoice = PreInvoice(**safe_data)
            db.session.add(pre_invoice)
            db.session.commit()
            
            assert pre_invoice.can_edit is True
        
        # Test stati non modificabili
        for status in non_editable_statuses:
            safe_data = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                pre_invoice_number=f'PRE-NOEDIT-{status.upper()}',
                billing_period_start=date(2024, 6, 1),
                billing_period_end=date(2024, 6, 30),
                status=status,
                created_by=self.user.id
            )
            
            pre_invoice = PreInvoice(**safe_data)
            db.session.add(pre_invoice)
            db.session.commit()
            
            assert pre_invoice.can_edit is False
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_can_send_external_property(self):
        """Test property can_send_external"""
        # Può essere inviata: ready + no external_invoice_id
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-CANSEND-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            status='ready',
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        
        assert pre_invoice.can_send_external is True
        
        # Non può essere inviata: status draft
        safe_data_draft = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-CANTSEND-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            status='draft',
            created_by=self.user.id
        )
        
        pre_invoice_draft = PreInvoice(**safe_data_draft)
        db.session.add(pre_invoice_draft)
        db.session.commit()
        
        assert pre_invoice_draft.can_send_external is False
        
        # Non può essere inviata: già inviata
        safe_data_sent = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-ALREADYSENT-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            status='ready',
            external_invoice_id='already_sent_123',
            created_by=self.user.id
        )
        
        pre_invoice_sent = PreInvoice(**safe_data_sent)
        db.session.add(pre_invoice_sent)
        db.session.commit()
        
        assert pre_invoice_sent.can_send_external is False
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_client_relationship(self):
        """Test relazione con Client"""
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-CLIENT-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        
        # Verifica relazione
        assert pre_invoice.client is not None
        assert pre_invoice.client.id == self.client_id
        assert pre_invoice.client_id == self.client_id
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_creator_relationship(self):
        """Test relazione con User (creator)"""
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-CREATOR-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        
        # Verifica relazione
        assert pre_invoice.creator is not None
        assert pre_invoice.creator.id == self.user.id
        assert pre_invoice.created_by == self.user.id
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_billing_periods(self):
        """Test diversi periodi di fatturazione"""
        billing_periods = [
            (date(2024, 1, 1), date(2024, 1, 31)),  # Gennaio
            (date(2024, 4, 1), date(2024, 6, 30)),  # Q2
            (date(2024, 1, 1), date(2024, 12, 31))  # Anno intero
        ]
        
        pre_invoices = []
        for i, (start_date, end_date) in enumerate(billing_periods):
            safe_data = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                pre_invoice_number=f'PRE-PERIOD-{i+1:03d}',
                billing_period_start=start_date,
                billing_period_end=end_date,
                created_by=self.user.id
            )
            
            pre_invoice = PreInvoice(**safe_data)
            pre_invoices.append(pre_invoice)
            db.session.add(pre_invoice)
        
        db.session.commit()
        
        # Verifica tutti i periodi
        for pre_invoice, (expected_start, expected_end) in zip(pre_invoices, billing_periods):
            assert pre_invoice.billing_period_start == expected_start
            assert pre_invoice.billing_period_end == expected_end
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_unique_number(self):
        """Test vincolo unique su pre_invoice_number"""
        # Prima pre-fattura
        safe_data_1 = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-UNIQUE-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            created_by=self.user.id
        )
        
        pre_invoice_1 = PreInvoice(**safe_data_1)
        db.session.add(pre_invoice_1)
        db.session.commit()
        
        # Tentativo di duplicato (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data_2 = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                pre_invoice_number='PRE-UNIQUE-001',  # Stesso numero
                billing_period_start=date(2024, 7, 1),
                billing_period_end=date(2024, 7, 31),
                created_by=self.user.id
            )
            pre_invoice_2 = PreInvoice(**safe_data_2)
            db.session.add(pre_invoice_2)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_validation(self):
        """Test validazione campi required"""
        # Test senza client_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                PreInvoice,
                pre_invoice_number='PRE-INVALID-001',
                billing_period_start=date(2024, 6, 1),
                billing_period_end=date(2024, 6, 30),
                created_by=self.user.id
                # client_id mancante
            )
            pre_invoice = PreInvoice(**safe_data)
            db.session.add(pre_invoice)
            db.session.commit()
        
        db.session.rollback()
        
        # Test senza pre_invoice_number (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                PreInvoice,
                client_id=self.client_id,
                billing_period_start=date(2024, 6, 1),
                billing_period_end=date(2024, 6, 30),
                created_by=self.user.id
                # pre_invoice_number mancante
            )
            pre_invoice = PreInvoice(**safe_data)
            db.session.add(pre_invoice)
            db.session.commit()
        
        db.session.rollback()
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-REPR-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            total_amount=Decimal('1500.00'),
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        
        repr_str = repr(pre_invoice)
        assert 'PRE-REPR-001' in repr_str
        assert '1500.00' in repr_str
    
    @requires_db_consistency(PreInvoice)
    def test_pre_invoice_deletion(self):
        """Test eliminazione pre-fattura"""
        safe_data = create_safe_data(
            PreInvoice,
            client_id=self.client_id,
            pre_invoice_number='PRE-DELETE-001',
            billing_period_start=date(2024, 6, 1),
            billing_period_end=date(2024, 6, 30),
            created_by=self.user.id
        )
        
        pre_invoice = PreInvoice(**safe_data)
        db.session.add(pre_invoice)
        db.session.commit()
        pre_invoice_id = pre_invoice.id
        
        # Elimina
        db.session.delete(pre_invoice)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(PreInvoice, pre_invoice_id)
        assert deleted is None