"""Unit tests for ResearchSession model."""
import pytest
from datetime import datetime
from models import ResearchSession, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestResearchSessionModel:
    """Test per il modello ResearchSession (CEO <PERSON> Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user
    
    @requires_table_exists(ResearchSession)
    def test_table_exists(self):
        """Verifica che la tabella research_sessions esista"""
        assert_table_exists(ResearchSession)
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_creation_basic(self):
        """Test creazione base di una research session"""
        # Usa create_safe_data per evitare problemi con campi inesistenti
        safe_data = create_safe_data(
            ResearchSession,
            title='Strategic Market Analysis',
            category='market_analysis',
            user_id=self.user.id,
            status='running'
        )
        
        session = ResearchSession(**safe_data)
        db.session.add(session)
        db.session.commit()
        
        assert session.id is not None
        assert session.title == 'Strategic Market Analysis'
        assert session.category == 'market_analysis'
        assert session.user_id == self.user.id
        assert session.status == 'running'
        assert session.created_at is not None
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_with_config(self):
        """Test creazione session con configurazione di ricerca"""
        research_config = {
            'query_templates': ['market_trend', 'competitor_analysis'],
            'depth': 'comprehensive',
            'sources': ['industry_reports', 'market_data']
        }
        
        company_context = {
            'industry': 'technology',
            'company_size': 'startup',
            'target_market': 'B2B_SaaS'
        }
        
        safe_data = create_safe_data(
            ResearchSession,
            title='Competitive Intelligence',
            category='competitor_analysis',
            user_id=self.user.id,
            research_config=research_config,
            company_context=company_context
        )
        
        session = ResearchSession(**safe_data)
        db.session.add(session)
        db.session.commit()
        
        assert session.research_config == research_config
        assert session.company_context == company_context
        assert session.category == 'competitor_analysis'
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_status_workflow(self):
        """Test workflow degli stati della session"""
        safe_data = create_safe_data(
            ResearchSession,
            title='Investment Analysis',
            category='investment',
            user_id=self.user.id
        )
        
        session = ResearchSession(**safe_data)
        db.session.add(session)
        db.session.commit()
        
        # Stato iniziale
        assert session.status == 'running'
        assert session.completed_at is None
        
        # Completamento
        session.status = 'completed'
        session.completed_at = datetime.utcnow()
        db.session.commit()
        
        assert session.status == 'completed'
        assert session.completed_at is not None
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_user_relationship(self):
        """Test relazione con User"""
        safe_data = create_safe_data(
            ResearchSession,
            title='Talent Strategy',
            category='talent',
            user_id=self.user.id
        )
        
        session = ResearchSession(**safe_data)
        db.session.add(session)
        db.session.commit()
        
        # Verifica relazione diretta
        assert session.user is not None
        assert session.user.id == self.user.id
        
        # Verifica FK constraint
        assert session.user_id == self.user.id
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_to_dict(self):
        """Test serializzazione to_dict"""
        safe_data = create_safe_data(
            ResearchSession,
            title='Market Expansion',
            category='market_analysis',
            user_id=self.user.id,
            status='completed'
        )
        
        session = ResearchSession(**safe_data)
        session.completed_at = datetime.utcnow()
        db.session.add(session)
        db.session.commit()
        
        data = session.to_dict()
        
        assert data['id'] == session.id
        assert data['title'] == 'Market Expansion'
        assert data['category'] == 'market_analysis'
        assert data['status'] == 'completed'
        assert 'created_at' in data
        assert 'completed_at' in data
        assert 'query_count' in data
        assert 'insights_count' in data
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_categories(self):
        """Test diverse categorie di ricerca"""
        categories = [
            'market_analysis',
            'competitor_analysis', 
            'investment',
            'talent',
            'technology_trends'
        ]
        
        sessions = []
        for i, category in enumerate(categories):
            safe_data = create_safe_data(
                ResearchSession,
                title=f'Research {i+1}',
                category=category,
                user_id=self.user.id
            )
            
            session = ResearchSession(**safe_data)
            sessions.append(session)
            db.session.add(session)
        
        db.session.commit()
        
        # Verifica che tutte le categorie siano state salvate
        for session, expected_category in zip(sessions, categories):
            assert session.category == expected_category
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_validation(self):
        """Test validazione campi required"""
        # Test senza user_id (dovrebbe fallire)
        with pytest.raises(Exception):  # IntegrityError o simile
            safe_data = create_safe_data(
                ResearchSession,
                title='Invalid Session',
                category='market_analysis'
                # user_id mancante
            )
            session = ResearchSession(**safe_data)
            db.session.add(session)
            db.session.commit()
        
        # Rollback after failed transaction
        db.session.rollback()
        
        # Test senza title (dovrebbe fallire)
        with pytest.raises(Exception):
            safe_data = create_safe_data(
                ResearchSession,
                category='market_analysis',
                user_id=self.user.id
                # title mancante
            )
            session = ResearchSession(**safe_data)
            db.session.add(session)
            db.session.commit()
        
        # Rollback after failed transaction  
        db.session.rollback()
    
    @requires_db_consistency(ResearchSession)
    def test_research_session_deletion(self):
        """Test eliminazione session"""
        safe_data = create_safe_data(
            ResearchSession,
            title='To Delete',
            category='test',
            user_id=self.user.id
        )
        
        session = ResearchSession(**safe_data)
        db.session.add(session)
        db.session.commit()
        session_id = session.id
        
        # Elimina
        db.session.delete(session)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = ResearchSession.query.get(session_id)
        assert deleted is None