"""Unit tests for PerformanceTemplate model."""
import pytest
from models import PerformanceTemplate, User
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestPerformanceTemplateModel:
    """Test per il modello PerformanceTemplate (Performance Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup per ogni test method"""
        self.app = app
        self.user = test_user  # test_user è l'oggetto User
    
    @requires_table_exists(PerformanceTemplate)
    def test_table_exists(self):
        """Verifica che la tabella performance_templates esista"""
        assert_table_exists(PerformanceTemplate)
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_creation_basic(self):
        """Test creazione base template performance"""
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template Sviluppatore Junior',
            description='Template di valutazione per sviluppatori junior',
            template_type='annual_review',
            job_level='junior',
            department='Sviluppo',
            created_by=self.user.id
        )
        
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        assert template.id is not None
        assert template.name == 'Template Sviluppatore Junior'
        assert template.template_type == 'annual_review'
        assert template.job_level == 'junior'
        assert template.department == 'Sviluppo'
        assert template.is_active is True  # Default value
        assert template.is_default is False  # Default value
        assert template.created_at is not None
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_different_types(self):
        """Test template con diversi tipi di valutazione"""
        template_types = [
            ('annual_review', 'Revisione Annuale'),
            ('quarterly_review', 'Revisione Trimestrale'),
            ('probation_review', 'Revisione Periodo di Prova'),
            ('promotion_review', 'Revisione per Promozione'),
            ('360_feedback', 'Feedback a 360 gradi')
        ]
        
        templates = []
        for template_type, name in template_types:
            safe_data = create_safe_data(
                PerformanceTemplate,
                name=name,
                description=f'Template per {name.lower()}',
                template_type=template_type,
                created_by=self.user.id
            )
            
            template = PerformanceTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica tutti i tipi
        for template, (expected_type, expected_name) in zip(templates, template_types):
            assert template.template_type == expected_type
            assert template.name == expected_name
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_job_levels(self):
        """Test template per diversi livelli di lavoro"""
        job_level_scenarios = [
            ('intern', 'Tirocinante'),
            ('junior', 'Junior'),
            ('mid', 'Mid-level'),
            ('senior', 'Senior'),
            ('lead', 'Team Lead'),
            ('manager', 'Manager'),
            ('director', 'Director')
        ]
        
        templates = []
        for job_level, level_name in job_level_scenarios:
            safe_data = create_safe_data(
                PerformanceTemplate,
                name=f'Template {level_name}',
                description=f'Template di valutazione per posizione {level_name}',
                job_level=job_level,
                template_type='annual_review',
                created_by=self.user.id
            )
            
            template = PerformanceTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica livelli
        for template, (expected_level, level_name) in zip(templates, job_level_scenarios):
            assert template.job_level == expected_level
            assert level_name in template.name
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_departments(self):
        """Test template per diversi dipartimenti"""
        department_scenarios = [
            'Sviluppo',
            'Design',
            'Marketing',
            'Vendite',
            'Risorse Umane',
            'Amministrazione',
            'Supporto Clienti',
            'Quality Assurance'
        ]
        
        templates = []
        for department in department_scenarios:
            safe_data = create_safe_data(
                PerformanceTemplate,
                name=f'Template {department}',
                description=f'Template specifico per il dipartimento {department}',
                department=department,
                template_type='annual_review',
                created_by=self.user.id
            )
            
            template = PerformanceTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica dipartimenti
        for template, expected_department in zip(templates, department_scenarios):
            assert template.department == expected_department
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_evaluation_criteria(self):
        """Test template con criteri di valutazione dettagliati"""
        detailed_criteria = [
            'Competenze Tecniche: Conoscenza linguaggi di programmazione, architetture software, best practices',
            'Comunicazione: Capacità di comunicare efficacemente con team e stakeholder',
            'Gestione Progetti: Pianificazione, organizzazione, rispetto delle scadenze',
            'Leadership: Capacità di guidare e ispirare il team, decision making',
            'Innovazione: Proposte migliorative, creatività, pensiero critico'
        ]
        
        templates = []
        for i, criteria in enumerate(detailed_criteria):
            safe_data = create_safe_data(
                PerformanceTemplate,
                name=f'Template Dettagliato {i+1}',
                description=f'Template con criteri specifici area {i+1}',
                evaluation_criteria=criteria,
                template_type='annual_review',
                created_by=self.user.id
            )
            
            template = PerformanceTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica criteri dettagliati
        for template, expected_criteria in zip(templates, detailed_criteria):
            assert template.evaluation_criteria == expected_criteria
            assert len(template.evaluation_criteria) > 50
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_rating_scales(self):
        """Test template con diverse scale di valutazione"""
        rating_scale_scenarios = [
            ('1-5 Scale', '1=Insufficiente, 2=Migliorabile, 3=Soddisfacente, 4=Buono, 5=Eccellente'),
            ('Percentuale', '0-100% dove 100% rappresenta il raggiungimento completo degli obiettivi'),
            ('Lettere', 'A=Eccellente, B=Buono, C=Soddisfacente, D=Migliorabile, F=Insufficiente'),
            ('Descrittiva', 'Supera le aspettative / Soddisfa le aspettative / Non soddisfa le aspettative')
        ]
        
        templates = []
        for scale_type, scale_description in rating_scale_scenarios:
            safe_data = create_safe_data(
                PerformanceTemplate,
                name=f'Template {scale_type}',
                description=f'Template con scala di valutazione {scale_type}',
                rating_scale=scale_description,
                template_type='annual_review',
                created_by=self.user.id
            )
            
            template = PerformanceTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica scale di rating
        for template, (scale_type, expected_scale) in zip(templates, rating_scale_scenarios):
            assert template.rating_scale == expected_scale
            assert scale_type in template.name
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_target_roles(self):
        """Test template per ruoli target specifici"""
        target_role_scenarios = [
            'Frontend Developer',
            'Backend Developer',
            'Full Stack Developer',
            'DevOps Engineer',
            'Product Manager',
            'UI/UX Designer',
            'Data Scientist',
            'Quality Assurance Engineer'
        ]
        
        templates = []
        for target_role in target_role_scenarios:
            safe_data = create_safe_data(
                PerformanceTemplate,
                name=f'Template {target_role}',
                description=f'Template specializzato per ruolo {target_role}',
                target_role=target_role,
                template_type='annual_review',
                created_by=self.user.id
            )
            
            template = PerformanceTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica ruoli target
        for template, expected_role in zip(templates, target_role_scenarios):
            assert template.target_role == expected_role
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_fields_config(self):
        """Test template con configurazione campi JSON"""
        fields_config_scenarios = [
            '{"fields": ["technical_skills", "communication", "teamwork"], "weights": [40, 30, 30]}',
            '{"sections": ["objectives", "competencies", "development"], "required": ["objectives"]}',
            '{"custom_fields": [{"name": "innovation", "type": "rating", "scale": "1-5"}]}',
            '{"evaluation_period": "annual", "review_cycle": "yearly", "participants": ["self", "manager", "peers"]}'
        ]
        
        templates = []
        for i, fields_config in enumerate(fields_config_scenarios):
            safe_data = create_safe_data(
                PerformanceTemplate,
                name=f'Template Config {i+1}',
                description=f'Template con configurazione JSON {i+1}',
                fields_config=fields_config,
                template_type='annual_review',
                created_by=self.user.id
            )
            
            template = PerformanceTemplate(**safe_data)
            templates.append(template)
            db.session.add(template)
        
        db.session.commit()
        
        # Verifica configurazioni JSON
        for template, expected_config in zip(templates, fields_config_scenarios):
            assert template.fields_config == expected_config
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_active_status(self):
        """Test stato attivo/inattivo dei template"""
        # Template attivo
        safe_data_active = create_safe_data(
            PerformanceTemplate,
            name='Template Attivo',
            description='Template attualmente in uso',
            is_active=True,
            created_by=self.user.id
        )
        
        # Template inattivo
        safe_data_inactive = create_safe_data(
            PerformanceTemplate,
            name='Template Inattivo',
            description='Template non più in uso',
            is_active=False,
            created_by=self.user.id
        )
        
        template_active = PerformanceTemplate(**safe_data_active)
        template_inactive = PerformanceTemplate(**safe_data_inactive)
        
        db.session.add(template_active)
        db.session.add(template_inactive)
        db.session.commit()
        
        assert template_active.is_active is True
        assert template_inactive.is_active is False
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_default_flag(self):
        """Test template di default"""
        # Template di default
        safe_data_default = create_safe_data(
            PerformanceTemplate,
            name='Template Default',
            description='Template di default per tutte le valutazioni',
            is_default=True,
            created_by=self.user.id
        )
        
        # Template normale
        safe_data_normal = create_safe_data(
            PerformanceTemplate,
            name='Template Normale',
            description='Template specifico non di default',
            is_default=False,
            created_by=self.user.id
        )
        
        template_default = PerformanceTemplate(**safe_data_default)
        template_normal = PerformanceTemplate(**safe_data_normal)
        
        db.session.add(template_default)
        db.session.add(template_normal)
        db.session.commit()
        
        assert template_default.is_default is True
        assert template_normal.is_default is False
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_creator_relationship(self):
        """Test relazione con creatore"""
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template Test Creatore',
            description='Template per test relazione creatore',
            created_by=self.user.id
        )
        
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        # Verifica relazione con user
        assert template.created_by == self.user.id
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_complex_scenario(self):
        """Test scenario complesso con tutti i campi"""
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template Completo Senior Developer',
            description='Template completo per sviluppatori senior con tutti i criteri',
            template_type='annual_review',
            job_level='senior',
            department='Sviluppo',
            target_role='Senior Full Stack Developer',
            evaluation_criteria='Competenze tecniche avanzate, leadership, mentoring, innovazione, delivery',
            rating_scale='1-5 dove 5 è eccellente',
            fields_config='{"sections": ["technical", "leadership", "delivery"], "weights": [40, 30, 30]}',
            is_active=True,
            is_default=False,
            created_by=self.user.id
        )
        
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        # Verifica tutti i campi
        assert template.name == 'Template Completo Senior Developer'
        assert template.template_type == 'annual_review'
        assert template.job_level == 'senior'
        assert template.department == 'Sviluppo'
        assert template.target_role == 'Senior Full Stack Developer'
        assert 'leadership' in template.evaluation_criteria
        assert '1-5' in template.rating_scale
        assert 'technical' in template.fields_config
        assert template.is_active is True
        assert template.is_default is False
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_minimal_data(self):
        """Test template con dati minimi required"""
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template Minimo',
            created_by=self.user.id
            # Solo campi required
        )
        
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        assert template.name == 'Template Minimo'
        assert template.created_by == self.user.id
        assert template.is_active is True  # Default value
        assert template.is_default is False  # Default value
        assert template.description is None
        assert template.template_type is None
        assert template.job_level is None
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template Representation Test',
            created_by=self.user.id
        )
        
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        repr_str = repr(template)
        assert 'Template Representation Test' in repr_str
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_validation(self):
        """Test validazione campi required"""
        # Test senza name (dovrebbe fallire - name è nullable=False)
        with pytest.raises(Exception):  # IntegrityError
            safe_data = create_safe_data(
                PerformanceTemplate,
                description='Template senza nome',
                created_by=self.user.id
                # name mancante
            )
            template = PerformanceTemplate(**safe_data)
            db.session.add(template)
            db.session.commit()
        
        db.session.rollback()
        
        # Test template senza created_by (dovrebbe essere permesso - campo nullable)
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template senza creatore'
            # created_by mancante - questo è permesso
        )
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        # Verifica che il template sia stato creato
        assert template.id is not None
        assert template.name == 'Template senza creatore'
        assert template.created_by is None
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_update_tracking(self):
        """Test tracking degli aggiornamenti"""
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template Update Test',
            description='Template per test aggiornamenti',
            created_by=self.user.id
        )
        
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        
        original_updated_at = template.updated_at
        
        # Aggiorna alcuni campi
        template.description = 'Descrizione aggiornata'
        template.template_type = 'quarterly_review'
        template.is_active = False
        db.session.commit()
        
        # Verifica che updated_at sia cambiato
        assert template.updated_at != original_updated_at
        assert template.description == 'Descrizione aggiornata'
        assert template.template_type == 'quarterly_review'
        assert template.is_active is False
    
    @requires_db_consistency(PerformanceTemplate)
    def test_performance_template_deletion(self):
        """Test eliminazione template"""
        safe_data = create_safe_data(
            PerformanceTemplate,
            name='Template da Eliminare',
            description='Questo template sarà eliminato',
            created_by=self.user.id
        )
        
        template = PerformanceTemplate(**safe_data)
        db.session.add(template)
        db.session.commit()
        template_id = template.id
        
        # Elimina
        db.session.delete(template)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(PerformanceTemplate, template_id)
        assert deleted is None