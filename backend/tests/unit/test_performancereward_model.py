"""Unit tests for PerformanceReward model."""
import pytest
from models import PerformanceReward
from extensions import db
from tests.helpers.db_consistency import (
    requires_table_exists, 
    requires_db_consistency,
    create_safe_data,
    assert_table_exists
)


class TestPerformanceRewardModel:
    """Test per il modello PerformanceReward (Performance Module)"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, app):
        """Setup per ogni test method"""
        self.app = app
    
    @requires_table_exists(PerformanceReward)
    def test_table_exists(self):
        """Verifica che la tabella performance_rewards esista"""
        assert_table_exists(PerformanceReward)
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_creation_basic(self):
        """Test creazione base reward performance"""
        safe_data = create_safe_data(
            PerformanceReward,
            title='Employee of the Month'
        )
        
        reward = PerformanceReward(**safe_data)
        db.session.add(reward)
        db.session.commit()
        
        assert reward.id is not None
        assert reward.title == 'Employee of the Month'
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_different_types(self):
        """Test reward con diversi tipi di riconoscimenti"""
        reward_types = [
            'Employee of the Month',
            'Outstanding Performance Award',
            'Innovation Excellence',
            'Team Player Recognition',
            'Leadership Excellence',
            'Customer Service Hero',
            'Technical Achievement Award',
            'Best Collaboration Award'
        ]
        
        rewards = []
        for title in reward_types:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica tutti i reward
        for reward, expected_title in zip(rewards, reward_types):
            assert reward.title == expected_title
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_monetary_awards(self):
        """Test reward monetari"""
        monetary_rewards = [
            'Bonus Performance €500',
            'Quarterly Incentive €1000', 
            'Annual Excellence Bonus €2500',
            'Project Completion Bonus €750',
            'Innovation Award €1500',
            'Sales Achievement €800',
            'Quality Excellence €600',
            'Customer Satisfaction Bonus €400'
        ]
        
        rewards = []
        for title in monetary_rewards:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica reward monetari
        for reward, expected_title in zip(rewards, monetary_rewards):
            assert reward.title == expected_title
            assert '€' in reward.title  # Contiene simbolo euro
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_non_monetary(self):
        """Test reward non monetari"""
        non_monetary_rewards = [
            'Extra Vacation Day',
            'Flexible Working Hours',
            'Remote Work Privilege',
            'Training Course Sponsorship',
            'Conference Attendance',
            'Parking Space Priority',
            'Team Lunch Treat',
            'Company Recognition Certificate'
        ]
        
        rewards = []
        for title in non_monetary_rewards:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica reward non monetari
        for reward, expected_title in zip(rewards, non_monetary_rewards):
            assert reward.title == expected_title
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_achievement_levels(self):
        """Test reward per diversi livelli di achievement"""
        achievement_levels = [
            'Bronze Achievement Award',
            'Silver Excellence Recognition',
            'Gold Performance Award',
            'Platinum Leadership Honor',
            'Diamond Innovation Prize',
            'Master Level Expertise',
            'Champion of Quality',
            'Legend Status Achievement'
        ]
        
        rewards = []
        for title in achievement_levels:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica livelli achievement
        for reward, expected_title in zip(rewards, achievement_levels):
            assert reward.title == expected_title
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_department_specific(self):
        """Test reward specifici per dipartimento"""
        department_rewards = [
            'Development Team MVP',
            'Marketing Campaign Excellence',
            'Sales Target Crusher',
            'HR Innovation Award',
            'Design Creativity Prize',
            'QA Quality Champion',
            'Support Hero of the Month',
            'Operations Efficiency Master'
        ]
        
        rewards = []
        for title in department_rewards:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica reward dipartimentali
        for reward, expected_title in zip(rewards, department_rewards):
            assert reward.title == expected_title
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_italian_titles(self):
        """Test reward con titoli in italiano"""
        italian_rewards = [
            'Dipendente del Mese',
            'Premio Eccellenza Performance',
            'Riconoscimento Innovazione',
            'Award Collaborazione Team',
            'Certificato Qualità Servizio',
            'Trofeo Leadership Aziendale',
            'Medaglia Merito Tecnico',
            'Attestato Soddisfazione Cliente'
        ]
        
        rewards = []
        for title in italian_rewards:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica titoli italiani
        for reward, expected_title in zip(rewards, italian_rewards):
            assert reward.title == expected_title
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_long_titles(self):
        """Test reward con titoli dettagliati"""
        detailed_rewards = [
            'Outstanding Achievement in Cross-Functional Project Leadership and Team Work',
            'Excellence in Customer Service Delivery with Exceptional Problem-Solving',
            'Innovation Award for Creative Technical Solutions and Process Optimization',
            'Recognition for Mentoring Excellence and Knowledge Transfer to Juniors'
        ]
        
        rewards = []
        for title in detailed_rewards:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica titoli dettagliati
        for reward, expected_title in zip(rewards, detailed_rewards):
            assert reward.title == expected_title
            # Verifica che i titoli siano relativamente lunghi (>65 char)
            assert len(reward.title) > 65
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_duplicate_titles(self):
        """Test reward con titoli duplicati (permessi)"""
        duplicate_title = 'Employee of the Month'
        
        rewards = []
        for i in range(3):
            safe_data = create_safe_data(
                PerformanceReward,
                title=duplicate_title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica duplicati permessi
        for reward in rewards:
            assert reward.id is not None
            assert reward.title == duplicate_title
        
        # Verifica ID diversi
        ids = [reward.id for reward in rewards]
        assert len(set(ids)) == 3
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_without_title(self):
        """Test reward senza titolo (permesso - campo nullable)"""
        safe_data = create_safe_data(PerformanceReward)
        
        reward = PerformanceReward(**safe_data)
        db.session.add(reward)
        db.session.commit()
        
        assert reward.id is not None
        assert reward.title is None
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_empty_title(self):
        """Test reward con titolo vuoto"""
        safe_data = create_safe_data(
            PerformanceReward,
            title=''
        )
        
        reward = PerformanceReward(**safe_data)
        db.session.add(reward)
        db.session.commit()
        
        assert reward.id is not None
        assert reward.title == ''
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_special_characters(self):
        """Test reward con caratteri speciali"""
        special_rewards = [
            'Top Performer 2024 🏆',
            'Innovation Award (Q1)',
            'Excellence @ Work',
            'Team Player #1',
            'Customer Hero - 5⭐',
            'Quality Champion & Process Improver',
            'Sales Star: 150% Target Achievement',
            'Leadership Excellence 💪'
        ]
        
        rewards = []
        for title in special_rewards:
            safe_data = create_safe_data(
                PerformanceReward,
                title=title
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica caratteri speciali
        for reward, expected_title in zip(rewards, special_rewards):
            assert reward.title == expected_title
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_repr(self):
        """Test rappresentazione string del modello"""
        safe_data = create_safe_data(
            PerformanceReward,
            title='Test Reward Representation'
        )
        
        reward = PerformanceReward(**safe_data)
        db.session.add(reward)
        db.session.commit()
        
        repr_str = repr(reward)
        # Verifica rappresentazione
        assert 'PerformanceReward' in repr_str or 'Test Reward Representation' in repr_str
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_deletion(self):
        """Test eliminazione reward"""
        safe_data = create_safe_data(
            PerformanceReward,
            title='Reward da Eliminare'
        )
        
        reward = PerformanceReward(**safe_data)
        db.session.add(reward)
        db.session.commit()
        reward_id = reward.id
        
        # Elimina
        db.session.delete(reward)
        db.session.commit()
        
        # Verifica eliminazione
        deleted = db.session.get(PerformanceReward, reward_id)
        assert deleted is None
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_bulk_creation(self):
        """Test creazione massiva reward"""
        reward_count = 15
        rewards = []
        
        for i in range(reward_count):
            safe_data = create_safe_data(
                PerformanceReward,
                title=f'Bulk Reward {i+1:02d}'
            )
            
            reward = PerformanceReward(**safe_data)
            rewards.append(reward)
            db.session.add(reward)
        
        db.session.commit()
        
        # Verifica creazione massiva
        assert len(rewards) == reward_count
        for i, reward in enumerate(rewards):
            assert reward.id is not None
            assert reward.title == f'Bulk Reward {i+1:02d}'
    
    @requires_db_consistency(PerformanceReward)
    def test_performance_reward_minimal_data(self):
        """Test reward con dati minimi"""
        safe_data = create_safe_data(PerformanceReward)
        
        reward = PerformanceReward(**safe_data)
        db.session.add(reward)
        db.session.commit()
        
        assert reward.id is not None
        assert reward.title is None