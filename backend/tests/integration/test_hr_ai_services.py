"""
Test di integrazione per HR AI Services e HR Assistant module.
Testa l'integrazione con APIs AI per HR chatbot e knowledge management.
"""
import pytest
import json
from datetime import datetime, date
from unittest.mock import Mock, patch, AsyncMock

from services.hr_ai_service import HRAIService


class TestHRAIService:
    """Test per HR AI Service chatbot e knowledge management"""

    def test_hr_ai_service_initialization(self, app):
        """Test inizializzazione HR AI Service"""
        with app.app_context():
            service = HRAIService()
            
            assert service is not None
            assert hasattr(service, 'openai_api_key')
            assert hasattr(service, 'perplexity_api_key')
            assert len(service.hr_categories) == 10
            assert 'contracts' in service.hr_categories
            assert 'onboarding' in service.hr_categories

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_process_hr_query_contracts(self, mock_client, app, db_session):
        """Test processing query HR su contratti"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "category": "contracts",
                            "response": "Il tuo contratto prevede 22 giorni di ferie all'anno. Puoi consultare i dettagli nel portale HR sezione 'Il Mio Contratto'.",
                            "confidence": 0.92,
                            "follow_up_questions": [
                                "Hai bisogno di informazioni sui permessi retribuiti?",
                                "Vuoi sapere come richiedere le ferie?"
                            ],
                            "relevant_policies": ["Politica Ferie", "Contratto Nazionale"],
                            "escalation_needed": False,
                            "satisfaction_survey": True
                        })
                    }
                }],
                "usage": {"total_tokens": 180}
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            user_message = "Quanti giorni di ferie ho a disposizione?"
            result = await service.process_hr_query(user_message, user_id=1, session_id="test_session")
            
            assert result["success"] is True
            assert result["response"]["category"] == "contracts"
            assert "22 giorni" in result["response"]["response"]
            assert result["response"]["confidence"] > 0.9
            assert len(result["response"]["follow_up_questions"]) == 2
            assert result["response"]["escalation_needed"] is False

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_process_hr_query_onboarding(self, mock_client, app, db_session):
        """Test processing query HR su onboarding"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "category": "onboarding",
                            "response": "Benvenuto in DatPortal! La tua checklist di onboarding include: 1) Setup computer e account, 2) Meeting con HR, 3) Introduzione al team. Riceverai una email con tutti i dettagli.",
                            "confidence": 0.95,
                            "checklist_items": [
                                "Setup computer aziendale",
                                "Configurazione account email e sistemi",
                                "Meeting introduttivo con HR Manager",
                                "Presentazione al team di riferimento",
                                "Completamento documentazione contrattuale"
                            ],
                            "next_steps": "Controlla la tua email per ricevere la checklist completa",
                            "hr_contact": "<EMAIL>",
                            "escalation_needed": False
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            user_message = "Sono un nuovo dipendente, cosa devo fare?"
            result = await service.process_hr_query(user_message, user_id=2, session_id="onboarding_session")
            
            assert result["success"] is True
            assert result["response"]["category"] == "onboarding"
            assert "Benvenuto" in result["response"]["response"]
            assert len(result["response"]["checklist_items"]) == 5
            assert "<EMAIL>" in result["response"]["hr_contact"]

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_process_hr_query_escalation_needed(self, mock_client, app, db_session):
        """Test query HR che richiede escalation"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "category": "complex_issue",
                            "response": "La tua richiesta riguarda una situazione specifica che richiede supporto diretto da parte di HR. Ti metterò in contatto con un HR Manager.",
                            "confidence": 0.88,
                            "escalation_needed": True,
                            "escalation_reason": "Richiesta complessa che necessita review manuale",
                            "priority_level": "high",
                            "estimated_response_time": "2-4 ore lavorative",
                            "hr_manager_assigned": "Maria Rossi",
                            "ticket_number": "HR-2025-001234"
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            user_message = "Ho un problema personale che sta influenzando il mio lavoro e ho bisogno di supporto"
            result = await service.process_hr_query(user_message, user_id=3, session_id="support_session")
            
            assert result["success"] is True
            assert result["response"]["escalation_needed"] is True
            assert result["response"]["priority_level"] == "high"
            assert "HR-2025-001234" in result["response"]["ticket_number"]
            assert result["response"]["hr_manager_assigned"] == "Maria Rossi"

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_generate_hr_content_template(self, mock_client, app, db_session):
        """Test generazione template contenuto HR"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "template_title": "Politica Smart Working - Template",
                            "template_category": "policies",
                            "content_sections": [
                                {
                                    "section_title": "Obiettivi del Smart Working",
                                    "content": "Il smart working in DatPortal mira a migliorare work-life balance..."
                                },
                                {
                                    "section_title": "Modalità di Richiesta",
                                    "content": "Per richiedere smart working: 1) Compila form dedicato..."
                                },
                                {
                                    "section_title": "Strumenti e Tecnologie",
                                    "content": "Vengono forniti: laptop aziendale, VPN, tools collaborativi..."
                                }
                            ],
                            "target_audience": "Tutti i dipendenti",
                            "approval_required": True,
                            "review_frequency": "annuale",
                            "related_policies": ["Codice Etico", "Regolamento Interno"]
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            content_request = {
                "type": "policy",
                "topic": "smart working",
                "target_audience": "all_employees",
                "company_context": {
                    "name": "DatPortal",
                    "size": "15 employees",
                    "industry": "Technology"
                }
            }
            
            result = await service.generate_hr_content_template(content_request)
            
            assert result["success"] is True
            assert result["template"]["template_category"] == "policies"
            assert len(result["template"]["content_sections"]) == 3
            assert result["template"]["approval_required"] is True
            assert "Smart Working" in result["template"]["template_title"]

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_analyze_employee_sentiment(self, mock_client, app, db_session):
        """Test analisi sentiment conversazioni employee"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "overall_sentiment": "positive",
                            "sentiment_score": 0.75,
                            "key_themes": [
                                "Soddisfazione per benefits aziendali",
                                "Apprezzamento per flessibilità orari",
                                "Richiesta maggiore formazione tecnica"
                            ],
                            "sentiment_trends": {
                                "benefits": 0.85,
                                "work_life_balance": 0.80,
                                "training": 0.60,
                                "communication": 0.70
                            },
                            "action_recommendations": [
                                "Potenziare programma formazione tecnica",
                                "Mantenere current benefit package",
                                "Implementare feedback più frequente"
                            ],
                            "priority_areas": ["training_enhancement", "feedback_improvement"],
                            "employee_satisfaction_indicators": {
                                "retention_risk": "low",
                                "engagement_level": "high",
                                "improvement_suggestions": 3
                            }
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            conversation_data = [
                {"user_id": 1, "message": "Sono molto soddisfatto dei benefit aziendali"},
                {"user_id": 2, "message": "Mi piace la flessibilità degli orari"},
                {"user_id": 3, "message": "Vorrei più opportunità di formazione tecnica"}
            ]
            
            result = await service.analyze_employee_sentiment(conversation_data, time_period="last_month")
            
            assert result["success"] is True
            assert result["analysis"]["overall_sentiment"] == "positive"
            assert result["analysis"]["sentiment_score"] == 0.75
            assert len(result["analysis"]["key_themes"]) == 3
            assert len(result["analysis"]["action_recommendations"]) == 3
            assert result["analysis"]["employee_satisfaction_indicators"]["retention_risk"] == "low"

    @pytest.mark.asyncio
    async def test_hr_query_error_handling(self, app):
        """Test gestione errori HR AI Service"""
        with app.app_context():
            service = HRAIService()
            
            # Test error in API call
            with patch.object(service, '_process_with_openai', side_effect=Exception("API Error")):
                result = await service.process_hr_query(
                    "Test query", 
                    user_id=1,
                    session_id="test"
                )
                
                assert result["success"] is False
                assert "error" in result
                assert "Mi dispiace" in result["fallback_response"]

    def test_hr_categories_coverage(self, app):
        """Test copertura categorie HR supportate"""
        with app.app_context():
            service = HRAIService()
            
            # Verifica che tutte le categorie principali HR siano coperte
            expected_categories = [
                'contracts', 'onboarding', 'offboarding', 'leave', 'permits',
                'travel', 'benefits', 'tools', 'purchases', 'training'
            ]
            
            for category in expected_categories:
                assert category in service.hr_categories
                assert len(service.hr_categories[category]) > 0
                
            # Test categorization logic
            assert service._categorize_hr_query("Come richiedo le ferie?") == "leave"
            assert service._categorize_hr_query("Sono un nuovo dipendente") == "onboarding"
            assert service._categorize_hr_query("Dettagli del mio contratto") == "contracts"


class TestHRAIContentGeneration:
    """Test per generazione contenuti HR con AI"""

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_generate_onboarding_checklist(self, mock_client, app):
        """Test generazione checklist onboarding personalizzata"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "checklist_title": "Onboarding Checklist - Sviluppatore Senior",
                            "role_specific_items": [
                                "Setup ambiente di sviluppo (Git, Docker, IDE)",
                                "Accesso repository codebase principale",
                                "Review architettura software DatPortal",
                                "Meeting tecnico con Tech Lead"
                            ],
                            "general_items": [
                                "Firma contratto e documentazione HR",
                                "Setup email aziendale e account Slack",
                                "Introduzione team e organizzazione",
                                "Presentazione vision e valori aziendali"
                            ],
                            "timeline": {
                                "day_1": ["Firma contratto", "Setup computer"],
                                "week_1": ["Meeting team", "Setup tecnico"],
                                "month_1": ["Review performance iniziale"]
                            },
                            "completion_criteria": "Tutti i task completati e feedback positivo da manager",
                            "estimated_duration": "2-3 settimane"
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            employee_profile = {
                "role": "Senior Developer",
                "department": "Engineering",
                "experience_level": "senior",
                "technical_skills": ["Python", "React", "Docker"]
            }
            
            result = await service.generate_onboarding_checklist(employee_profile)
            
            assert result["success"] is True
            assert "Senior" in result["checklist"]["checklist_title"]
            assert len(result["checklist"]["role_specific_items"]) == 4
            assert "Git" in result["checklist"]["role_specific_items"][0]
            assert "day_1" in result["checklist"]["timeline"]

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_generate_performance_review_template(self, mock_client, app):
        """Test generazione template performance review"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "review_template_title": "Performance Review - Q1 2025",
                            "evaluation_areas": [
                                {
                                    "area": "Technical Skills",
                                    "weight": 0.4,
                                    "criteria": ["Code quality", "Problem solving", "Technology adoption"],
                                    "rating_scale": "1-5 (5=Exceptional)"
                                },
                                {
                                    "area": "Collaboration",
                                    "weight": 0.3,
                                    "criteria": ["Team work", "Communication", "Knowledge sharing"],
                                    "rating_scale": "1-5 (5=Exceptional)"
                                },
                                {
                                    "area": "Goal Achievement",
                                    "weight": 0.3,
                                    "criteria": ["Project delivery", "Quality standards", "Timeline adherence"],
                                    "rating_scale": "1-5 (5=Exceptional)"
                                }
                            ],
                            "review_questions": [
                                "What were your main achievements this quarter?",
                                "Which goals did you find most challenging?",
                                "What support do you need for next quarter?"
                            ],
                            "development_planning": {
                                "skill_gaps_assessment": True,
                                "career_progression_discussion": True,
                                "training_recommendations": True
                            },
                            "next_review_date": "2025-07-01"
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            review_config = {
                "review_period": "Q1 2025",
                "employee_role": "Software Developer",
                "department": "Engineering",
                "review_type": "quarterly"
            }
            
            result = await service.generate_performance_review_template(review_config)
            
            assert result["success"] is True
            assert "Q1 2025" in result["template"]["review_template_title"]
            assert len(result["template"]["evaluation_areas"]) == 3
            assert result["template"]["evaluation_areas"][0]["weight"] == 0.4
            assert len(result["template"]["review_questions"]) == 3


class TestHRAIKnowledgeManagement:
    """Test per gestione knowledge base HR con AI"""

    @pytest.mark.asyncio
    @patch('services.hr_ai_service.httpx.AsyncClient')
    async def test_analyze_hr_knowledge_gaps(self, mock_client, app, db_session):
        """Test analisi gap nella knowledge base HR"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "knowledge_gaps": [
                                {
                                    "topic": "Remote Work Policy",
                                    "gap_severity": "high",
                                    "frequency_asked": 15,
                                    "current_coverage": "partial",
                                    "recommended_action": "Create comprehensive remote work guide"
                                },
                                {
                                    "topic": "Professional Development Budget",
                                    "gap_severity": "medium", 
                                    "frequency_asked": 8,
                                    "current_coverage": "missing",
                                    "recommended_action": "Document training budget policy"
                                }
                            ],
                            "content_priorities": [
                                "Remote work procedures and guidelines",
                                "Professional development and training policies",
                                "Performance evaluation criteria details"
                            ],
                            "user_behavior_insights": {
                                "most_asked_categories": ["leave", "benefits", "tools"],
                                "escalation_rate": 0.12,
                                "satisfaction_score": 0.78
                            },
                            "recommendations": [
                                "Prioritize remote work policy documentation",
                                "Create video tutorials for common procedures",
                                "Implement proactive content suggestions"
                            ]
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = HRAIService()
            
            # Mock HR query data for analysis
            query_data = [
                {"query": "How to request remote work?", "category": "unknown", "resolved": False},
                {"query": "What's my training budget?", "category": "unknown", "resolved": False}
            ]
            
            result = await service.analyze_hr_knowledge_gaps(query_data, analysis_period="last_quarter")
            
            assert result["success"] is True
            assert len(result["analysis"]["knowledge_gaps"]) == 2
            assert result["analysis"]["knowledge_gaps"][0]["gap_severity"] == "high"
            assert result["analysis"]["user_behavior_insights"]["escalation_rate"] == 0.12
            assert len(result["analysis"]["recommendations"]) == 3

    @pytest.mark.asyncio 
    async def test_hr_ai_workflow_integration(self, app, db_session):
        """Test integrazione workflow completo HR AI"""
        with app.app_context():
            service = HRAIService()
            
            # Test workflow: Query → Response → Feedback → Learning
            with patch.object(service, 'process_hr_query') as mock_query, \
                 patch.object(service, 'record_user_feedback') as mock_feedback, \
                 patch.object(service, 'update_knowledge_base') as mock_update:
                
                # Setup mock responses
                mock_query.return_value = {
                    "success": True,
                    "response": {"category": "leave", "confidence": 0.9},
                    "session_id": "test_session"
                }
                mock_feedback.return_value = {"success": True, "feedback_recorded": True}
                mock_update.return_value = {"success": True, "knowledge_updated": True}
                
                # Step 1: Process user query
                query_result = await service.process_hr_query(
                    "Come richiedo le ferie?", 
                    user_id=1, 
                    session_id="workflow_test"
                )
                
                assert query_result["success"] is True
                assert query_result["response"]["confidence"] == 0.9
                
                # Step 2: Record user feedback
                feedback_result = await service.record_user_feedback(
                    session_id="workflow_test",
                    feedback_rating=5,
                    feedback_comment="Risposta molto utile"
                )
                
                assert feedback_result["success"] is True
                
                # Step 3: Update knowledge base (if needed)
                if query_result["response"]["confidence"] < 0.8:
                    update_result = await service.update_knowledge_base(
                        query="Come richiedo le ferie?",
                        category="leave",
                        improved_response="Updated response content"
                    )
                
                # Verify workflow completed successfully
                mock_query.assert_called_once()
                mock_feedback.assert_called_once()
                # mock_update might not be called if confidence is high