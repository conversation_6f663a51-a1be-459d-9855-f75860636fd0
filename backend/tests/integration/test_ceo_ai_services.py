"""
Test di integrazione per CEO AI Services e Human CEO module.
Testa l'integrazione con APIs AI per strategic intelligence e business insights.
"""
import pytest
import json
from datetime import datetime, date
from unittest.mock import Mock, patch, AsyncMock

from services.ceo_ai_service import CEOAIService


class TestCEOAIService:
    """Test per CEO AI Service strategic intelligence"""

    def test_ceo_ai_service_initialization(self, app):
        """Test inizializzazione CEO AI Service"""
        with app.app_context():
            service = CEOAIService()
            
            assert service is not None
            assert hasattr(service, 'openai_api_key')
            assert hasattr(service, 'perplexity_api_key')
            assert service.openai_base_url == "https://api.openai.com/v1"
            assert service.perplexity_base_url == "https://api.perplexity.ai"

    @pytest.mark.asyncio
    @patch('services.ceo_ai_service.httpx.AsyncClient')
    async def test_generate_ceo_response_pro_mode(self, mock_client, app):
        """Test generazione response CEO in modalità pro"""
        with app.app_context():
            # Setup mock response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "executive_summary": "Analisi rapida del mercato technology Q1 2025",
                            "key_insights": [
                                "Crescita AI del 40% YoY nel settore",
                                "Opportunità nelle PMI per digitalizzazione"
                            ],
                            "strategic_recommendations": [
                                "Investire in AI automation tools",
                                "Espandere portfolio servizi PMI"
                            ],
                            "confidence_score": 85,
                            "urgency_level": "medium",
                            "action_items": [
                                {
                                    "action": "Market research approfondita AI tools",
                                    "timeline": "2 settimane",
                                    "priority": "high"
                                }
                            ]
                        })
                    }
                }],
                "usage": {"total_tokens": 250}
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = CEOAIService()
            
            query = "Analizza il mercato AI per le PMI italiane"
            context = {
                "company_profile": {
                    "name": "DatPortal Solutions", 
                    "sector": "Technology",
                    "revenue": "1.2M",
                    "employees": 15
                },
                "current_projects": 8,
                "active_clients": 25,
                "monthly_revenue": 120000
            }
            
            result = await service.generate_ceo_response(query, context, mode='pro')
            
            assert result["success"] is True
            assert "executive_summary" in result["ai_response"]
            assert len(result["ai_response"]["key_insights"]) == 2
            assert result["ai_response"]["confidence_score"] == 85
            assert len(result["ai_response"]["action_items"]) == 1
            assert result["mode"] == "pro"

    @pytest.mark.asyncio
    @patch('services.ceo_ai_service.httpx.AsyncClient')
    async def test_generate_ceo_response_deep_mode(self, mock_client, app):
        """Test generazione response CEO in modalità deep research"""
        with app.app_context():
            # Mock Perplexity response for deep research
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "research_summary": "Analisi approfondita trend digitalizzazione PMI",
                            "market_analysis": {
                                "market_size": "€2.4B mercato italiano digital transformation",
                                "growth_rate": "15% CAGR 2024-2027",
                                "key_players": ["Microsoft", "AWS", "Digital360"]
                            },
                            "competitive_landscape": [
                                "Forte frammentazione mercato consulenza",
                                "Opportunità specializzazione verticale"
                            ],
                            "strategic_opportunities": [
                                "Partnership con system integrator",
                                "Sviluppo soluzioni industry-specific"
                            ],
                            "risk_assessment": [
                                "Saturazione mercato basic digitalization",
                                "Competitive pressure da big players"
                            ],
                            "confidence_score": 92,
                            "research_depth": "comprehensive",
                            "sources_analyzed": 25
                        })
                    }
                }],
                "citations": [
                    "https://www.osservatori.net/it/ricerche/comunicati-stampa/digital-transformation-academy-mercato-digitale-italia",
                    "https://www.mise.gov.it/index.php/it/impresa-4-0"
                ],
                "usage": {"total_tokens": 850}
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = CEOAIService()
            
            query = "Comprehensive analysis of digital transformation market opportunities in Italy"
            context = {
                "company_profile": {
                    "name": "DatPortal Solutions",
                    "sector": "Digital Transformation",
                    "target_market": "Italian SMEs"
                }
            }
            
            result = await service.generate_ceo_response(query, context, mode='deep')
            
            assert result["success"] is True
            assert "market_analysis" in result["ai_response"]
            assert result["ai_response"]["confidence_score"] == 92
            assert result["ai_response"]["research_depth"] == "comprehensive"
            assert len(result["citations"]) == 2
            assert result["mode"] == "deep"

    @pytest.mark.asyncio
    async def test_generate_ceo_response_error_handling(self, app):
        """Test gestione errori CEO AI Service"""
        with app.app_context():
            service = CEOAIService()
            
            # Mock error in API call
            with patch.object(service, '_generate_pro_response', side_effect=Exception("API Error")):
                result = await service.generate_ceo_response(
                    "Test query", 
                    {"company": "test"}, 
                    mode='pro'
                )
                
                assert result["success"] is False
                assert "error" in result
                assert result["mode"] == "pro"
                assert "fallback" in result["ai_response"]["executive_summary"]

    @pytest.mark.asyncio
    @patch('services.ceo_ai_service.httpx.AsyncClient')
    async def test_generate_insights_from_conversation(self, mock_client, app):
        """Test generazione insights da conversazione AI"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "insight_title": "Market Expansion Strategy for Italian SMEs",
                            "insight_category": "strategic_analysis",
                            "key_points": [
                                "PMI italiane mostrano forte demand per soluzioni AI",
                                "Opportunity per partnership con system integrator"
                            ],
                            "business_impact": "high",
                            "implementation_timeline": "Q2-Q3 2025",
                            "confidence_level": 88,
                            "tags": ["market_expansion", "ai_adoption", "sme_segment"],
                            "action_recommendations": [
                                "Develop AI-ready solution portfolio",
                                "Establish SI partnership program"
                            ]
                        })
                    }
                }],
                "usage": {"total_tokens": 320}
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = CEOAIService()
            
            conversation_data = {
                "messages": [
                    {"role": "user", "content": "Analizza mercato AI per PMI"},
                    {"role": "assistant", "content": "Il mercato AI per PMI in Italia..."}
                ],
                "context": {"session_duration": 15, "topics_covered": ["AI", "PMI", "digitalization"]}
            }
            
            result = await service.generate_insights_from_conversation(conversation_data)
            
            assert result["success"] is True
            assert result["insight"]["insight_category"] == "strategic_analysis"
            assert result["insight"]["business_impact"] == "high"
            assert result["insight"]["confidence_level"] == 88
            assert len(result["insight"]["tags"]) == 3

    def test_ceo_ai_service_configuration(self, app):
        """Test configurazione e setup CEO AI Service"""
        with app.app_context():
            service = CEOAIService()
            
            # Test configuration methods exist
            assert hasattr(service, '_build_strategic_context')
            assert hasattr(service, '_format_ceo_response')
            assert hasattr(service, '_get_fallback_response')
            
            # Test API endpoints configuration
            assert "openai.com" in service.openai_base_url
            assert "perplexity.ai" in service.perplexity_base_url

    @pytest.mark.asyncio
    async def test_strategic_context_building(self, app):
        """Test costruzione contesto strategico per AI"""
        with app.app_context():
            service = CEOAIService()
            
            business_context = {
                "company_profile": {
                    "name": "DatPortal Solutions",
                    "sector": "Technology Consulting",
                    "revenue_ytd": 1200000,
                    "employees": 15
                },
                "performance_metrics": {
                    "active_projects": 8,
                    "client_satisfaction": 4.7,
                    "monthly_recurring_revenue": 120000
                },
                "market_position": {
                    "target_segments": ["SME", "Digital Transformation"],
                    "competitive_advantages": ["AI expertise", "Italian market focus"]
                }
            }
            
            # Test context enrichment
            enriched_context = await service._build_strategic_context(
                "Analizza performance aziendale Q1", 
                business_context
            )
            
            assert enriched_context is not None
            assert "company_intelligence" in enriched_context
            assert "strategic_priorities" in enriched_context
            assert "market_context" in enriched_context

    @pytest.mark.asyncio 
    @patch('services.ceo_ai_service.httpx.AsyncClient')
    async def test_ai_response_categorization(self, mock_client, app):
        """Test categorizzazione automatica response AI"""
        with app.app_context():
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "response_category": "financial_analysis",
                            "urgency_level": "high",
                            "stakeholders_involved": ["CFO", "Board"],
                            "follow_up_required": True,
                            "estimated_impact": "significant"
                        })
                    }
                }]
            }
            
            mock_async_client = AsyncMock()
            mock_async_client.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = CEOAIService()
            
            query = "Analizza impatto nuovo cliente enterprise su cash flow"
            context = {"financial_data": {"monthly_revenue": 120000}}
            
            result = await service._categorize_ceo_query(query, context)
            
            assert result["category"] == "financial_analysis"
            assert result["urgency_level"] == "high"
            assert result["follow_up_required"] is True
            assert "CFO" in result["stakeholders_involved"]


class TestCEOAIIntegrationWorkflow:
    """Test workflow completi CEO AI Service"""

    @pytest.mark.asyncio
    @patch('services.ceo_ai_service.httpx.AsyncClient')
    async def test_complete_ceo_consultation_workflow(self, mock_client, app):
        """Test workflow completo consultazione CEO con AI"""
        with app.app_context():
            # Mock responses per workflow multi-step
            responses = [
                # Initial analysis
                {
                    "choices": [{
                        "message": {
                            "content": json.dumps({
                                "executive_summary": "Strategic analysis for market expansion",
                                "key_insights": ["Market opportunity in AI consulting"],
                                "confidence_score": 85,
                                "requires_deep_research": True
                            })
                        }
                    }]
                },
                # Deep research follow-up
                {
                    "choices": [{
                        "message": {
                            "content": json.dumps({
                                "comprehensive_analysis": "Detailed market research results",
                                "strategic_recommendations": ["Partnership strategy", "Investment plan"],
                                "risk_mitigation": ["Market timing", "Resource allocation"],
                                "confidence_score": 92
                            })
                        }
                    }],
                    "citations": ["source1.com", "source2.com"]
                },
                # Insights generation
                {
                    "choices": [{
                        "message": {
                            "content": json.dumps({
                                "insight_title": "AI Market Expansion Strategy",
                                "business_impact": "high",
                                "implementation_priority": "immediate"
                            })
                        }
                    }]
                }
            ]
            
            mock_async_client = AsyncMock()
            # Configure mock to return different responses for each call
            mock_async_client.post.side_effect = [Mock(status_code=200, json=Mock(return_value=resp)) for resp in responses]
            mock_client.return_value.__aenter__.return_value = mock_async_client
            
            service = CEOAIService()
            
            # Step 1: Initial query
            initial_query = "Should we expand into AI consulting services?"
            context = {
                "company_profile": {"name": "DatPortal", "sector": "Technology"},
                "current_capabilities": ["Web Development", "Digital Transformation"]
            }
            
            initial_result = await service.generate_ceo_response(initial_query, context, mode='pro')
            
            assert initial_result["success"] is True
            assert initial_result["ai_response"]["requires_deep_research"] is True
            
            # Step 2: Deep research (triggered by requires_deep_research)
            deep_result = await service.generate_ceo_response(initial_query, context, mode='deep')
            
            assert deep_result["success"] is True
            assert "comprehensive_analysis" in deep_result["ai_response"]
            assert len(deep_result["citations"]) == 2
            
            # Step 3: Generate actionable insights
            conversation_data = {
                "messages": [
                    {"role": "user", "content": initial_query},
                    {"role": "assistant", "content": str(initial_result["ai_response"])},
                    {"role": "assistant", "content": str(deep_result["ai_response"])}
                ]
            }
            
            insights_result = await service.generate_insights_from_conversation(conversation_data)
            
            assert insights_result["success"] is True
            assert insights_result["insight"]["business_impact"] == "high"
            
            # Verify complete workflow metrics
            total_confidence = (
                initial_result["ai_response"]["confidence_score"] + 
                deep_result["ai_response"]["confidence_score"]
            ) / 2
            assert total_confidence > 85  # High confidence in strategic analysis

    @pytest.mark.asyncio
    async def test_ceo_ai_error_recovery_workflow(self, app):
        """Test recovery da errori nei workflow CEO AI"""
        with app.app_context():
            service = CEOAIService()
            
            # Test recovery da timeout
            with patch.object(service, '_generate_pro_response', side_effect=asyncio.TimeoutError):
                result = await service.generate_ceo_response(
                    "Market analysis", 
                    {"company": "test"}, 
                    mode='pro'
                )
                
                assert result["success"] is False
                assert "timeout" in result["error"].lower()
                assert result["ai_response"]["executive_summary"] is not None  # Fallback content
                
            # Test recovery da API rate limit
            with patch.object(service, '_generate_deep_response', side_effect=Exception("Rate limit exceeded")):
                result = await service.generate_ceo_response(
                    "Comprehensive analysis", 
                    {"company": "test"}, 
                    mode='deep'
                )
                
                assert result["success"] is False
                assert "rate limit" in result["error"].lower()
                assert result["mode"] == "deep"