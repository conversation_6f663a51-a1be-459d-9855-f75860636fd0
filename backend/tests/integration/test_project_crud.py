import pytest
from datetime import date, datetime, timedelta
from models import Project, User, Task, ProjectResource, TaskDependency, ProjectKPI, KPI

# --- Test per Project ---

def test_create_project(app, db_session):
    """Test creazione di un nuovo progetto"""
    with app.app_context():
        project = Project(
            name="Test Project",
            description="Progetto di test CRUD",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31),
            status="active",
            budget=50000
        )
        db_session.add(project)
        db_session.commit()

        # Verifica che il progetto abbia un ID (è stato inserito nel DB)
        assert project.id is not None

        # Recupera il progetto dal DB e verifica i dati
        saved_project = db_session.get(Project, project.id)
        assert saved_project.name == "Test Project"
        assert saved_project.description == "Progetto di test CRUD"
        assert saved_project.budget == 50000

def test_read_project(app, db_session):
    """Test lettura di un progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Read Project Test",
            description="Progetto per test di lettura",
            start_date=date(2024, 7, 1),
            end_date=date(2024, 10, 15),
            status="planning"
        )
        db_session.add(project)
        db_session.commit()
        project_id = project.id

        # Recupera il progetto e verifica i dati
        retrieved = db_session.get(Project, project_id)
        assert retrieved.name == "Read Project Test"
        assert retrieved.status == "planning"
        assert retrieved.start_date == date(2024, 7, 1)

def test_update_project(app, db_session):
    """Test aggiornamento di un progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Update Project Test",
            description="Da aggiornare",
            start_date=date(2024, 8, 1),
            end_date=date(2024, 12, 31),
            status="planning"
        )
        db_session.add(project)
        db_session.commit()
        project_id = project.id

        # Aggiorna il progetto
        project.name = "Updated Project"
        project.status = "active"
        project.description = "Descrizione aggiornata"
        db_session.commit()

        # Verifica l'aggiornamento
        updated = db_session.get(Project, project_id)
        assert updated.name == "Updated Project"
        assert updated.status == "active"
        assert updated.description == "Descrizione aggiornata"

def test_delete_project(app, db_session):
    """Test eliminazione di un progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Delete Project Test",
            description="Da cancellare",
            start_date=date(2024, 9, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()
        project_id = project.id

        # Elimina il progetto
        db_session.delete(project)
        db_session.commit()

        # Verifica che il progetto sia stato eliminato
        deleted = db_session.get(Project, project_id)
        assert deleted is None

# --- Test per ProjectResource ---

def test_create_project_resource(app, db_session, test_user_id):
    """Test creazione di una risorsa di progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Resource Test Project",
            description="Progetto per test risorse",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea una risorsa (allocazione utente al progetto)
        resource = ProjectResource(
            project_id=project.id,
            user_id=test_user_id,
            allocation_percentage=75,
            role="developer"
        )
        db_session.add(resource)
        db_session.commit()

        # Verifica inserimento
        assert resource.id is not None
        assert resource.project_id == project.id
        assert resource.user_id == test_user_id

        # Verifica relazioni
        saved_resource = db_session.get(ProjectResource, resource.id)
        assert saved_resource.project.name == "Resource Test Project"
        assert saved_resource.user is not None

def test_update_project_resource(app, db_session, test_user_id):
    """Test aggiornamento di una risorsa di progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Update Resource Project",
            description="Progetto per test aggiornamento risorse",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea una risorsa
        resource = ProjectResource(
            project_id=project.id,
            user_id=test_user_id,
            allocation_percentage=50,
            role="analyst"
        )
        db_session.add(resource)
        db_session.commit()
        resource_id = resource.id

        # Aggiorna la risorsa
        resource.allocation_percentage = 100
        resource.role = "lead developer"
        db_session.commit()

        # Verifica aggiornamento
        updated = db_session.get(ProjectResource, resource_id)
        assert updated.allocation_percentage == 100
        assert updated.role == "lead developer"

def test_delete_project_resource(app, db_session, test_user_id):
    """Test eliminazione di una risorsa di progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Delete Resource Project",
            description="Progetto per test eliminazione risorse",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea una risorsa
        resource = ProjectResource(
            project_id=project.id,
            user_id=test_user_id,
            allocation_percentage=25,
            role="tester"
        )
        db_session.add(resource)
        db_session.commit()
        resource_id = resource.id

        # Elimina la risorsa
        db_session.delete(resource)
        db_session.commit()

        # Verifica eliminazione
        deleted = db_session.get(ProjectResource, resource_id)
        assert deleted is None

# --- Test per TaskDependency ---

def test_create_task_dependency(app, db_session):
    """Test creazione di una dipendenza tra task"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Dependency Test Project",
            description="Progetto per test dipendenze",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea due task di test con i nuovi campi
        task1 = Task(
            name="Task 1",
            description="Task precedente",
            project_id=project.id,
            status="pending",
            start_date=date(2024, 6, 1),
            due_date=date(2024, 6, 7),
            estimated_hours=40.0
        )
        task2 = Task(
            name="Task 2",
            description="Task dipendente",
            project_id=project.id,
            status="pending",
            start_date=date(2024, 6, 8),
            due_date=date(2024, 6, 14),
            estimated_hours=32.0
        )
        db_session.add_all([task1, task2])
        db_session.commit()

        # Crea una dipendenza
        dependency = TaskDependency(
            task_id=task2.id,
            depends_on_id=task1.id
        )
        db_session.add(dependency)
        db_session.commit()

        # Verifica inserimento
        assert dependency.id is not None
        assert dependency.task_id == task2.id
        assert dependency.depends_on_id == task1.id

        # Verifica relazioni
        saved_dependency = db_session.get(TaskDependency, dependency.id)
        assert saved_dependency.task.name == "Task 2"
        assert saved_dependency.depends_on.name == "Task 1"

def test_delete_task_dependency(app, db_session):
    """Test eliminazione di una dipendenza tra task"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Delete Dependency Project",
            description="Progetto per test eliminazione dipendenze",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea due task di test
        task1 = Task(
            name="Task A",
            description="Task precedente",
            project_id=project.id,
            status="pending"
        )
        task2 = Task(
            name="Task B",
            description="Task dipendente",
            project_id=project.id,
            status="pending"
        )
        db_session.add_all([task1, task2])
        db_session.commit()

        # Crea una dipendenza
        dependency = TaskDependency(
            task_id=task2.id,
            depends_on_id=task1.id
        )
        db_session.add(dependency)
        db_session.commit()
        dependency_id = dependency.id

        # Elimina la dipendenza
        db_session.delete(dependency)
        db_session.commit()

        # Verifica eliminazione
        deleted = db_session.get(TaskDependency, dependency_id)
        assert deleted is None

# --- Test per ProjectKPI ---

def test_create_project_kpi(app, db_session):
    """Test creazione di un KPI di progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="KPI Test Project",
            description="Progetto per test KPI",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea un KPI
        kpi = KPI(
            name="Completamento attività",
            description="Percentuale di completamento delle attività",
            target_value=100,
            current_value=45,
            unit="%",
            category="performance"
        )
        db_session.add(kpi)
        db_session.commit()

        # Collega KPI al progetto
        project_kpi = ProjectKPI(
            project_id=project.id,
            kpi_id=kpi.id,
            target_value=100,
            current_value=45
        )
        db_session.add(project_kpi)
        db_session.commit()

        # Verifica inserimento
        assert project_kpi.id is not None
        assert project_kpi.project_id == project.id
        assert project_kpi.kpi_id == kpi.id

        # Verifica relazioni
        saved_project_kpi = db_session.get(ProjectKPI, project_kpi.id)
        assert saved_project_kpi.project.name == "KPI Test Project"
        assert saved_project_kpi.kpi.name == "Completamento attività"

def test_update_project_kpi(app, db_session):
    """Test aggiornamento di un KPI di progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Update KPI Project",
            description="Progetto per test aggiornamento KPI",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea un KPI
        kpi = KPI(
            name="Budget utilizzato",
            description="Percentuale di budget utilizzato",
            target_value=100,
            current_value=30,
            unit="%",
            category="finanziario"
        )
        db_session.add(kpi)
        db_session.commit()

        # Collega KPI al progetto
        project_kpi = ProjectKPI(
            project_id=project.id,
            kpi_id=kpi.id,
            target_value=100,
            current_value=30
        )
        db_session.add(project_kpi)
        db_session.commit()
        project_kpi_id = project_kpi.id

        # Aggiorna il KPI del progetto
        project_kpi.current_value = 65
        db_session.commit()

        # Verifica aggiornamento
        updated = db_session.get(ProjectKPI, project_kpi_id)
        assert updated.current_value == 65

def test_delete_project_kpi(app, db_session):
    """Test eliminazione di un KPI di progetto"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Delete KPI Project",
            description="Progetto per test eliminazione KPI",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea un KPI
        kpi = KPI(
            name="Soddisfazione cliente",
            description="Livello di soddisfazione del cliente",
            target_value=10,
            current_value=8,
            unit="voto",
            category="cliente"
        )
        db_session.add(kpi)
        db_session.commit()

        # Collega KPI al progetto
        project_kpi = ProjectKPI(
            project_id=project.id,
            kpi_id=kpi.id,
            target_value=10,
            current_value=8
        )
        db_session.add(project_kpi)
        db_session.commit()
        project_kpi_id = project_kpi.id

        # Elimina il collegamento KPI-Progetto
        db_session.delete(project_kpi)
        db_session.commit()

        # Verifica eliminazione
        deleted = db_session.get(ProjectKPI, project_kpi_id)
        assert deleted is None

# --- Test per nuove funzionalità Task ---

def test_task_with_planning_fields(app, db_session):
    """Test creazione task con start_date e estimated_hours"""
    with app.app_context():
        # Crea un progetto di test
        project = Project(
            name="Planning Test Project",
            description="Progetto per test pianificazione task",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Crea task con pianificazione completa
        task = Task(
            name="Planned Task",
            description="Task con pianificazione completa",
            project_id=project.id,
            status="todo",
            priority="medium",
            start_date=date(2024, 6, 15),
            due_date=date(2024, 6, 22),
            estimated_hours=56.0
        )
        db_session.add(task)
        db_session.commit()

        # Verifica inserimento
        assert task.id is not None
        assert task.start_date == date(2024, 6, 15)
        assert task.due_date == date(2024, 6, 22)
        assert task.estimated_hours == 56.0

        # Verifica proprietà calcolate
        assert task.duration_days == 8  # 22-15+1 = 8 giorni
        assert task.actual_hours == 0.0  # Nessun timesheet ancora
        assert task.hours_variance is None  # Nessun timesheet
        assert task.hours_efficiency is None  # Nessun timesheet

def test_task_properties_with_timesheet(app, db_session, test_user_id):
    """Test proprietà calcolate del task con timesheet"""
    with app.app_context():
        # Crea progetto e task
        project = Project(
            name="Timesheet Test Project",
            description="Progetto per test timesheet",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        task = Task(
            name="Task with Timesheet",
            description="Task per test timesheet",
            project_id=project.id,
            estimated_hours=40.0,
            start_date=date(2024, 6, 15),
            due_date=date(2024, 6, 22)
        )
        db_session.add(task)
        db_session.commit()

        # Aggiungi timesheet (35 ore effettive vs 40 stimate)
        from models import TimesheetEntry
        timesheet1 = TimesheetEntry(
            user_id=test_user_id,
            project_id=project.id,
            task_id=task.id,
            date=date(2024, 6, 16),
            hours=8.0,
            description="Lavoro giorno 1"
        )
        timesheet2 = TimesheetEntry(
            user_id=test_user_id,
            project_id=project.id,
            task_id=task.id,
            date=date(2024, 6, 17),
            hours=7.5,
            description="Lavoro giorno 2"
        )
        timesheet3 = TimesheetEntry(
            user_id=test_user_id,
            project_id=project.id,
            task_id=task.id,
            date=date(2024, 6, 18),
            hours=8.0,
            description="Lavoro giorno 3"
        )
        timesheet4 = TimesheetEntry(
            user_id=test_user_id,
            project_id=project.id,
            task_id=task.id,
            date=date(2024, 6, 19),
            hours=6.5,
            description="Lavoro giorno 4"
        )
        timesheet5 = TimesheetEntry(
            user_id=test_user_id,
            project_id=project.id,
            task_id=task.id,
            date=date(2024, 6, 20),
            hours=5.0,
            description="Lavoro giorno 5"
        )

        db_session.add_all([timesheet1, timesheet2, timesheet3, timesheet4, timesheet5])
        db_session.commit()

        # Verifica proprietà calcolate
        assert task.actual_hours == 35.0  # 8+7.5+8+6.5+5
        assert task.hours_variance == -5.0  # 35-40 = -5 (sotto stima)
        assert abs(task.hours_efficiency - 114.29) < 0.1  # (40/35)*100 ≈ 114.29%

def test_task_date_validation(app, db_session):
    """Test validazione date task"""
    with app.app_context():
        # Crea progetto
        project = Project(
            name="Validation Test Project",
            description="Progetto per test validazione",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 12, 31)
        )
        db_session.add(project)
        db_session.commit()

        # Test task con date valide
        valid_task = Task(
            name="Valid Task",
            project_id=project.id,
            start_date=date(2024, 6, 15),
            due_date=date(2024, 6, 22),
            estimated_hours=40.0
        )
        db_session.add(valid_task)
        db_session.commit()

        # Verifica che il task sia stato creato
        assert valid_task.id is not None
        assert valid_task.duration_days == 8

        # Test task con solo start_date
        start_only_task = Task(
            name="Start Only Task",
            project_id=project.id,
            start_date=date(2024, 6, 15),
            estimated_hours=16.0
        )
        db_session.add(start_only_task)
        db_session.commit()

        assert start_only_task.duration_days is None  # Nessuna due_date

        # Test task con solo due_date
        due_only_task = Task(
            name="Due Only Task",
            project_id=project.id,
            due_date=date(2024, 6, 22),
            estimated_hours=24.0
        )
        db_session.add(due_only_task)
        db_session.commit()

        assert due_only_task.duration_days is None  # Nessuna start_date