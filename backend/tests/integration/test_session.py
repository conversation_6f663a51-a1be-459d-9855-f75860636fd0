import pytest
import json
from flask import url_for
from models import User # Assicurati che User sia importato

def test_access_after_logout(client, db_session, test_user_id, new_user_data):
    # Recupera l'utente dal DB
    user = db_session.get(User, test_user_id)
    assert user is not None, f"Test user with id {test_user_id} not found."

    # Login - API format
    login_response = client.post(url_for('api_auth.login'), 
                                data=json.dumps({
                                    'username': user.username,
                                    'password': new_user_data['password'], 
                                }),
                                content_type='application/json')
    
    assert login_response.status_code == 200, "Login should succeed"
    login_data = json.loads(login_response.data)
    assert login_data['success'] is True, "Login should return success"

    # Logout - API format
    logout_response = client.post(url_for('api_auth.logout'), content_type='application/json')
    assert logout_response.status_code == 200, "Logout should succeed"
    logout_data = json.loads(logout_response.data)
    assert logout_data['success'] is True, "Logout should return success"

    # Prova ad accedere a un endpoint API protetto dopo logout
    protected_response = client.get(url_for('api_auth.get_current_user'))
    
    # Dovrebbe restituire 401 unauthorized
    assert protected_response.status_code == 401, \
           "Protected API endpoint should return 401 after logout"
    
    # Verifica il formato della risposta di errore se presente
    if protected_response.data:
        try:
            error_data = json.loads(protected_response.data)
            assert error_data['success'] is False, "Unauthorized access should return success=False"
        except json.JSONDecodeError:
            # Se non è JSON, va bene comunque - l'importante è il 401
            pass 