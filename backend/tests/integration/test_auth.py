import pytest
import json
from flask import url_for
from models import User # Assicurati che User sia importato

def test_login_logout(client, db_session, test_user_id, new_user_data): # Usa test_user_id e new_user_data
    # Recupera l'utente dal DB
    user = db_session.get(User, test_user_id)
    assert user is not None, f"Test user with id {test_user_id} not found."

    # Login - API endpoint expects JSON, not form data
    response = client.post(url_for('api_auth.login'), 
                          data=json.dumps({
                              'username': user.username, # Usa l'username dell'oggetto User recuperato
                              'password': new_user_data['password'],  # Prendi la password da new_user_data come in conftest
                          }),
                          content_type='application/json')
    
    # Verifica che il login sia andato a buon fine
    assert response.status_code == 200
    # API response format verification
    login_data = json.loads(response.data)
    assert login_data['success'] is True, "Login API should return success=True"
    assert 'user' in login_data['data'], "Login response should contain user data"
    assert login_data['data']['user']['username'] == user.username, "Username should match in response"
    assert 'permissions' in login_data['data']['user'], "User should have permissions in response"

    # Logout - API endpoint uses POST method
    response_logout = client.post(url_for('api_auth.logout'), content_type='application/json')
    # Verifica che il logout sia andato a buon fine
    assert response_logout.status_code == 200
    
    # API response format verification for logout
    logout_data = json.loads(response_logout.data)
    assert logout_data['success'] is True, "Logout API should return success=True"
    assert 'Logout successful' in logout_data['message'], "Logout should return success message"
    
    # Verifichiamo che l'utente sia effettivamente disconnesso provando
    # ad accedere a un endpoint API che richiede autenticazione
    protected_response = client.get(url_for('api_auth.get_current_user'))
    
    # Dopo logout, dovrebbe restituire errore di autenticazione
    assert protected_response.status_code == 401, \
           "Protected API endpoint should return 401 after logout"
           
    # Verifica il messaggio di errore API
    if protected_response.data:
        try:
            error_data = json.loads(protected_response.data)
            assert error_data['success'] is False, "Unauthorized access should return success=False"
        except json.JSONDecodeError:
            # Se non è JSON, va bene comunque - l'importante è il 401
            pass 