"""
Test di integrazione per i servizi AI di DatPortal.
Testa l'integrazione con OpenAI e Perplexity APIs, CV parsing, resource optimization, ecc.
"""
import pytest
import json
import os
from datetime import datetime, date, timedelta
from unittest.mock import Mock, patch, MagicMock

from flask import current_app
from services.ai import (
    AIService, ai_service, analyze_text_with_openai, 
    generate_summary_with_openai, extract_insights_with_openai,
    analyze_project_requirements, generate_funding_recommendations,
    extract_skills_from_cv, generate_cv_html,
    analyze_resource_allocation, predict_resource_conflicts,
    generate_detailed_funding_analysis, search_funding_opportunities,
    generate_case_study_with_ai, optimize_team_composition
)


class TestAIServiceBasic:
    """Test base per il servizio AI unificato"""

    def test_ai_service_initialization(self, app):
        """Test inizializzazione del servizio AI"""
        with app.app_context():
            # Test singleton instance
            assert ai_service is not None
            assert isinstance(ai_service, AIService)
            
            # Test lazy initialization
            assert ai_service.openai_client is None
            
    @patch('services.ai.OPENAI_API_KEY', 'test-key-123')
    @patch('services.ai.OpenAI')
    def test_openai_client_creation(self, mock_openai, app):
        """Test creazione client OpenAI"""
        with app.app_context():
            mock_client = Mock()
            mock_openai.return_value = mock_client
            
            client = ai_service.get_openai_client()
            
            assert client == mock_client
            mock_openai.assert_called_once_with(api_key='test-key-123')
            
    def test_openai_client_missing_key(self, app):
        """Test errore quando manca API key"""
        with app.app_context():
            # Create a fresh AIService instance with no API key
            from services.ai import AIService
            test_service = AIService()
            
            with patch('services.ai.OPENAI_API_KEY', None):
                with pytest.raises(ValueError, match="OPENAI_API_KEY environment variable not set"):
                    test_service.get_openai_client()

    @patch('services.ai.OPENAI_API_KEY', 'test-key')
    @patch('services.ai.OpenAI')
    def test_query_openai_success(self, mock_openai, app):
        """Test query OpenAI con successo"""
        with app.app_context():
            # Setup mock response
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "AI response content"
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            # Create fresh service instance to avoid cached client
            from services.ai import AIService
            test_service = AIService()
            
            # Test query
            result = test_service.query_openai("Test prompt", model="gpt-4o-mini")
            
            assert result["success"] is True
            assert result["response"] == "AI response content"
            assert result["model"] == "gpt-4o-mini"
            
            # Verify API call
            mock_client.chat.completions.create.assert_called_once_with(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": "Test prompt"}],
                max_tokens=500,
                temperature=0.7
            )

    @patch('services.ai.OPENAI_API_KEY', 'test-key')
    @patch('services.ai.OpenAI')
    def test_query_openai_failure(self, mock_openai, app):
        """Test gestione errori query OpenAI"""
        with app.app_context():
            mock_client = Mock()
            mock_client.chat.completions.create.side_effect = Exception("API Error")
            mock_openai.return_value = mock_client
            
            # Create fresh service instance
            from services.ai import AIService
            test_service = AIService()
            
            result = test_service.query_openai("Test prompt")
            
            assert result["success"] is False
            assert "API Error" in result["error"]
            assert result["response"] is None


class TestCVProcessingAI:
    """Test per processing CV con AI"""

    @patch('services.ai.get_openai_client')
    def test_extract_skills_from_cv_success(self, mock_get_client, app):
        """Test estrazione skills da CV"""
        with app.app_context():
            # Mock response con skills strutturate
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "skills": [
                    {"name": "Python", "category": "Linguaggi di programmazione", "level": "advanced"},
                    {"name": "Django", "category": "Framework e librerie", "level": "intermediate"},
                    {"name": "PostgreSQL", "category": "Database", "level": "intermediate"}
                ],
                "summary": "Sviluppatore Python con 5 anni di esperienza",
                "experience_years": 5
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            cv_text = "Sviluppatore Python con esperienza in Django e PostgreSQL..."
            result = extract_skills_from_cv(cv_text)
            
            assert "error" not in result
            assert len(result["skills"]) == 3
            assert result["skills"][0]["name"] == "Python"
            assert result["skills"][0]["category"] == "Linguaggi di programmazione"
            assert result["experience_years"] == 5
            assert "Python" in result["summary"]
            
            # Verify AI prompt structure
            mock_client.chat.completions.create.assert_called_once()
            call_args = mock_client.chat.completions.create.call_args
            assert "CV:" in call_args[1]['messages'][0]['content']
            assert "Linguaggi di programmazione" in call_args[1]['messages'][0]['content']

    @patch('services.ai.get_openai_client')
    def test_extract_skills_from_cv_error(self, mock_get_client, app):
        """Test gestione errori estrazione skills"""
        with app.app_context():
            mock_client = Mock()
            mock_client.chat.completions.create.side_effect = Exception("OpenAI Error")
            mock_get_client.return_value = mock_client
            
            result = extract_skills_from_cv("CV content")
            
            assert "error" in result
            assert result["skills"] == []
            assert result["experience_years"] == 0

    @patch('services.ai.get_openai_client')
    def test_generate_cv_html_success(self, mock_get_client, app):
        """Test generazione HTML CV"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "<html><body><h1>Mario Rossi</h1><p>Sviluppatore Python</p></body></html>"
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            user_data = {
                "first_name": "Mario",
                "last_name": "Rossi",
                "email": "<EMAIL>"
            }
            skills_data = [
                {"name": "Python", "category": "Programming", "level": "advanced"}
            ]
            
            result = generate_cv_html(user_data, skills_data)
            
            assert "<html>" in result
            assert "Mario Rossi" in result
            assert "Sviluppatore Python" in result
            
            # Verify data was passed to AI
            mock_client.chat.completions.create.assert_called_once()
            call_args = mock_client.chat.completions.create.call_args
            prompt_content = call_args[1]['messages'][0]['content']
            assert "Mario" in prompt_content
            assert "Python" in prompt_content


class TestResourceAllocationAI:
    """Test per AI resource allocation e optimization"""

    @patch('services.ai.get_openai_client')
    def test_analyze_resource_allocation_success(self, mock_get_client, app):
        """Test analisi allocazione risorse con AI"""
        with app.app_context():
            # Mock AI response con suggerimenti
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "recommended_allocations": [
                    {
                        "user_id": 1,
                        "user_name": "Mario Rossi",
                        "role": "Senior Developer",
                        "allocation": 80
                    },
                    {
                        "user_id": 2,
                        "user_name": "Giulia Bianchi",
                        "role": "Frontend Developer",
                        "allocation": 60
                    }
                ],
                "optimization_insights": [
                    "Mario Rossi ha competenze Python perfette per questo progetto",
                    "Giulia Bianchi può gestire la parte frontend React"
                ],
                "potential_conflicts": [],
                "efficiency_score": 85,
                "cost_analysis": {
                    "total_monthly_cost": 15000,
                    "cost_per_day": 500
                }
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            # Test data
            project_data = {
                "name": "E-commerce Platform",
                "description": "Sviluppo piattaforma e-commerce",
                "project_type": "web_development",
                "budget": 50000,
                "required_skills": ["Python", "React", "PostgreSQL"]
            }
            
            available_resources = [
                {
                    "user_id": 1,
                    "name": "Mario Rossi",
                    "skills": ["Python", "Django", "PostgreSQL"],
                    "current_allocation": 20,
                    "daily_rate": 400
                },
                {
                    "user_id": 2,
                    "name": "Giulia Bianchi", 
                    "skills": ["React", "JavaScript", "CSS"],
                    "current_allocation": 40,
                    "daily_rate": 350
                }
            ]
            
            result = analyze_resource_allocation(project_data, available_resources)
            
            assert "error" not in result
            assert len(result["recommended_allocations"]) == 2
            assert result["recommended_allocations"][0]["user_name"] == "Mario Rossi"
            assert result["recommended_allocations"][0]["allocation"] == 80
            assert result["efficiency_score"] == 85
            assert len(result["optimization_insights"]) == 2
            assert "Mario Rossi" in result["optimization_insights"][0]
            
            # Verify AI prompt included real user names
            mock_client.chat.completions.create.assert_called_once()
            call_args = mock_client.chat.completions.create.call_args
            prompt_content = str(call_args[1]['messages'][1]['content'])
            assert "Mario Rossi" in prompt_content
            assert "Giulia Bianchi" in prompt_content

    @patch('services.ai.get_openai_client')
    def test_predict_resource_conflicts_success(self, mock_get_client, app):
        """Test predizione conflitti risorse"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "conflicts": [
                    {
                        "type": "overallocation",
                        "user": "Mario Rossi",
                        "current_allocation": 120,
                        "max_capacity": 100,
                        "severity": "high"
                    }
                ],
                "risk_level": "medium",
                "recommendations": [
                    "Ridurre allocazione Mario Rossi al 80%",
                    "Trovare risorsa aggiuntiva per Python"
                ],
                "timeline_impact": "Ritardo stimato 1-2 settimane se non risolto"
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            allocations_data = [
                {"user": "Mario Rossi", "allocation": 120, "projects": ["Proj A", "Proj B"]}
            ]
            timeline_data = {"duration_weeks": 12, "critical_path": ["Task1", "Task2"]}
            
            result = predict_resource_conflicts(allocations_data, timeline_data)
            
            assert "error" not in result
            assert len(result["conflicts"]) == 1
            assert result["conflicts"][0]["user"] == "Mario Rossi"
            assert result["risk_level"] == "medium"
            assert len(result["recommendations"]) == 2
            assert "timeline_impact" in result

    @patch('services.ai.get_openai_client')
    def test_optimize_team_composition_success(self, mock_get_client, app):
        """Test ottimizzazione composizione team"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "optimal_team": [
                    {"user_id": 1, "name": "Mario Rossi", "role": "Tech Lead", "allocation": 100},
                    {"user_id": 2, "name": "Giulia Bianchi", "role": "Frontend Dev", "allocation": 80},
                    {"user_id": 3, "name": "Luca Verdi", "role": "Backend Dev", "allocation": 90}
                ],
                "skill_coverage": 95,
                "team_synergy_score": 88,
                "alternative_options": [
                    {"description": "Team più junior con maggiore formazione"}
                ],
                "training_needs": [
                    "Formazione React avanzato per Luca Verdi"
                ]
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            project_requirements = {
                "skills_needed": ["Python", "React", "PostgreSQL"],
                "seniority_level": "senior",
                "team_size": 3
            }
            
            candidate_resources = [
                {"id": 1, "name": "Mario Rossi", "skills": ["Python", "Leadership"], "seniority": "senior"},
                {"id": 2, "name": "Giulia Bianchi", "skills": ["React", "CSS"], "seniority": "mid"},
                {"id": 3, "name": "Luca Verdi", "skills": ["Python", "API"], "seniority": "junior"}
            ]
            
            result = optimize_team_composition(project_requirements, candidate_resources)
            
            assert "error" not in result
            assert len(result["optimal_team"]) == 3
            assert result["skill_coverage"] == 95
            assert result["team_synergy_score"] == 88
            assert len(result["training_needs"]) == 1


class TestFundingAnalysisAI:
    """Test per analisi finanziamenti e bandi con AI"""

    @patch('services.ai.get_openai_client')
    def test_generate_detailed_funding_analysis_success(self, mock_get_client, app):
        """Test analisi dettagliata compatibilità bando"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "match_score": 85,
                "technical_compatibility": 90,
                "financial_feasibility": 80,
                "success_probability": 85,
                "recommendation": "Altamente raccomandato",
                "insights": [
                    "Perfetta compatibilità settoriale",
                    "Budget aziendale adeguato",
                    "Team con competenze richieste",
                    "Track record positivo"
                ],
                "strengths": [
                    "Esperienza consolidata in digitalizzazione",
                    "Team tecnico qualificato"
                ],
                "weaknesses": [
                    "Mancanza certificazione ISO",
                    "Portfolio progetti pubblici limitato"
                ],
                "next_steps": [
                    "Preparare documentazione tecnica",
                    "Costituire partnership strategica",
                    "Completare business plan dettagliato"
                ],
                "detailed_analysis": "L'azienda presenta un profilo molto solido per questo bando..."
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            company_profile = {
                "sector": "Technology",
                "size": "PMI",
                "skills": ["Python", "React", "Cloud"],
                "location": "Milano",
                "budget": "100k-500k"
            }
            
            funding_opportunity = {
                "title": "Digitalizzazione PMI 2025",
                "objectives": "Supportare digitalizzazione PMI",
                "target_sectors": ["Technology", "Manufacturing"],
                "budget_range": "50k-200k",
                "deadline": "2025-06-30"
            }
            
            result = generate_detailed_funding_analysis(company_profile, funding_opportunity)
            
            # The function uses Italian format, so check for Italian fields
            assert result["match_score"] == 85
            assert result["recommendation"] == "Altamente raccomandato"
            assert len(result["insights"]) == 4
            assert len(result["strengths"]) == 2
            assert len(result["next_steps"]) == 3
            assert "solido" in result["detailed_analysis"].lower()

    @patch('services.ai.PERPLEXITY_API_KEY', 'test-perplexity-key')
    @patch('services.ai.requests.post')
    def test_search_funding_opportunities_success(self, mock_post, app):
        """Test ricerca bandi con Perplexity AI"""
        with app.app_context():
            # Mock successful Perplexity response with structured JSON
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "bandi_trovati": [
                                {
                                    "titolo": "Bando Digitalizzazione PMI Piemonte 2025",
                                    "ente_erogatore": "Regione Piemonte",
                                    "importo_massimo": "€200.000",
                                    "percentuale_copertura": "70%",
                                    "scadenza": "2025-08-15",
                                    "settori_target": ["Technology", "Manufacturing"],
                                    "requisiti_principali": "PMI con sede in Piemonte",
                                    "link_ufficiale": "https://www.regione.piemonte.it/bandi",
                                    "motivo_compatibilita": "Settore technology, dimensione PMI",
                                    "descrizione_breve": "Bando per sostenere digitalizzazione delle PMI"
                                }
                            ]
                        })
                    }
                }],
                "citations": ["https://regione.piemonte.it"]
            }
            mock_post.return_value = mock_response
            
            company_profile = {
                "sector": "Technology",
                "size": "PMI",
                "location": "Torino"
            }
            
            result = search_funding_opportunities(company_profile)
            
            assert result["search_performed"] is True
            assert len(result["opportunities"]) == 1
            
            opportunity = result["opportunities"][0]
            assert opportunity["title"] == "Bando Digitalizzazione PMI Piemonte 2025"
            assert opportunity["source_entity"] == "Regione Piemonte"
            assert opportunity["max_amount_text"] == "€200.000"
            assert opportunity["parsed_from_json"] is True
            assert opportunity["match_score"] == 85  # High score for structured results
            
            # Verify API call to Perplexity
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert "https://api.perplexity.ai/chat/completions" in call_args[0]
            
            # Verify request structure
            request_data = call_args[1]['json']
            assert request_data['model'] == 'sonar-pro'  # Updated default model
            assert 'Technology' in str(request_data['messages'])

    @patch('services.ai.PERPLEXITY_API_KEY', None)
    def test_search_funding_opportunities_no_key(self, app):
        """Test ricerca bandi senza API key"""
        with app.app_context():
            result = search_funding_opportunities({"sector": "Technology"})
            
            assert result["search_performed"] is False
            assert "API key not configured" in result["error"]
            assert result["opportunities"] == []

    @patch('services.ai.get_openai_client')
    def test_generate_funding_recommendations_success(self, mock_get_client, app):
        """Test generazione raccomandazioni finanziamenti"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "funding_opportunities": [
                    "Horizon Europe Cluster Digital",
                    "PNRR Transizione 4.0",
                    "Bandi regionali digitalizzazione"
                ],
                "grant_programs": [
                    "Smart&Start Italia",
                    "Voucher Innovation Manager"
                ],
                "application_strategy": [
                    "Focus su innovazione tecnologica",
                    "Partnership con università",
                    "Documentare impatto sociale"
                ],
                "key_highlights": [
                    "Team tecnico qualificato",
                    "Track record progetti innovativi",
                    "Approccio sostenibile"
                ]
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            company_profile = {
                "sector": "Technology",
                "size": "Startup",
                "expertise": ["AI", "Web Development"]
            }
            
            result = generate_funding_recommendations(company_profile)
            
            assert "error" not in result
            assert len(result["funding_opportunities"]) == 3
            assert "Horizon Europe" in result["funding_opportunities"][0]
            assert len(result["application_strategy"]) == 3
            assert len(result["key_highlights"]) == 3


class TestProjectAnalysisAI:
    """Test per analisi progetti con AI"""

    @patch('services.ai.get_openai_client')
    def test_analyze_project_requirements_success(self, mock_get_client, app):
        """Test analisi requisiti progetto"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "key_deliverables": [
                    "Piattaforma web responsive",
                    "API REST complete",
                    "Dashboard amministrativa",
                    "App mobile iOS/Android"
                ],
                "potential_risks": [
                    "Integrazione sistemi legacy",
                    "Scalabilità database",
                    "Sicurezza dati sensibili"
                ],
                "resources_needed": [
                    "2 Senior Developers Python",
                    "1 Frontend Developer React",
                    "1 Mobile Developer",
                    "1 DevOps Engineer"
                ],
                "estimated_timeline": "16-20 settimane",
                "success_criteria": [
                    "Performance < 2s caricamento pagine",
                    "Uptime > 99.5%",
                    "Soddisfazione utenti > 4.5/5"
                ]
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            requirements_text = """
            Sviluppo di una piattaforma di e-commerce B2B per la gestione ordini, 
            inventario e fatturazione. Deve integrarsi con ERP SAP esistente e 
            supportare 1000+ utenti concorrenti.
            """
            
            result = analyze_project_requirements(requirements_text)
            
            assert "error" not in result
            assert len(result["key_deliverables"]) == 4
            assert "Piattaforma web" in result["key_deliverables"][0]
            assert len(result["potential_risks"]) == 3
            assert "legacy" in result["potential_risks"][0]
            assert len(result["resources_needed"]) == 4
            assert "Python" in result["resources_needed"][0]
            assert "settimane" in result["estimated_timeline"]

    @patch('services.ai.get_openai_client')
    def test_generate_case_study_with_ai_success(self, mock_get_client, app):
        """Test generazione case study con AI"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = json.dumps({
                "title": "Digitalizzazione Completa per ManufacturingCorp: +40% Efficienza e -30% Costi",
                "overview": "Abbiamo sviluppato una piattaforma integrata che ha trasformato i processi produttivi",
                "challenge": "Il cliente aveva processi manuali inefficienti e sistemi disconnessi",
                "solution": "Progettazione architettura microservizi con integrazione ERP e IoT",
                "implementation": "Metodologia agile con deploy incrementali in 4 fasi",
                "results": "Riduzione costi 30%, aumento efficienza 40%, ROI 180% primo anno",
                "technologies": ["Python", "React", "PostgreSQL", "Docker", "AWS"],
                "business_kpis": {
                    "time_reduction": 40,
                    "cost_reduction": 30,
                    "roi": 180,
                    "efficiency_gain": 45,
                    "user_satisfaction": 95
                },
                "primary_sector": "Manufacturing",
                "secondary_sectors": ["Logistics", "Supply Chain"],
                "implementation_duration": 120,
                "team_size": 6,
                "key_takeaways": [
                    "Importanza integrazione graduale",
                    "Change management cruciale",
                    "Monitoraggio KPI continuo"
                ],
                "target_audience": "prospect",
                "case_type": "success-story"
            })
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            prompt = "Genera case study per progetto digitalizzazione manufacturing"
            project_context = {
                "name": "Digital Factory Platform",
                "sector": "Manufacturing",
                "duration_weeks": 16,
                "team_size": 6
            }
            company_context = {
                "name": "DatPortal Solutions",
                "expertise": ["Digital Transformation", "IoT", "Data Analytics"]
            }
            
            result = generate_case_study_with_ai(
                prompt, 
                case_type="success-story", 
                target_audience="prospect",
                project_context=project_context,
                company_context=company_context
            )
            
            assert "error" not in result
            assert "+40%" in result["title"]
            assert "Abbiamo" in result["overview"]  # Prima persona
            assert result["business_kpis"]["roi"] == 180
            assert result["primary_sector"] == "Manufacturing"
            assert len(result["technologies"]) == 5
            assert result["implementation_duration"] == 120
            assert len(result["key_takeaways"]) == 3


class TestAIIntegrationErrors:
    """Test gestione errori e edge cases nei servizi AI"""

    @patch('services.ai.get_openai_client')
    def test_ai_service_network_timeout(self, mock_get_client, app):
        """Test timeout di rete"""
        with app.app_context():
            mock_client = Mock()
            mock_client.chat.completions.create.side_effect = Exception("Request timeout")
            mock_get_client.return_value = mock_client
            
            result = analyze_text_with_openai("Test text")
            
            assert "Error analyzing text: Request timeout" in result

    @patch('services.ai.get_openai_client')
    def test_ai_service_invalid_json_response(self, mock_get_client, app):
        """Test risposta JSON non valida"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "Invalid JSON content"
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            result = extract_insights_with_openai("Test text")
            
            assert "error" in result
            assert result["insights"] == []

    @patch('services.ai.get_openai_client')
    def test_ai_service_empty_response(self, mock_get_client, app):
        """Test risposta vuota"""
        with app.app_context():
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = ""
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            result = generate_summary_with_openai("Test text")
            
            assert result == ""  # Empty but valid response

    def test_ai_service_missing_config(self, app):
        """Test configurazione mancante"""
        with app.app_context():
            # Remove config temporarily
            with patch.dict(app.config, {}, clear=True):
                from services.ai import get_configured_model
                model = get_configured_model('analysis')
                assert model == 'gpt-4o-mini'  # Default fallback


class TestAIServiceConfiguration:
    """Test configurazione e setup servizi AI"""

    def test_get_configured_model_defaults(self, app):
        """Test configurazione modelli AI"""
        with app.app_context():
            from services.ai import get_configured_model
            
            # Test default model
            default_model = get_configured_model('default')
            assert default_model == 'gpt-4o-mini'
            
            # Test unknown model type
            unknown_model = get_configured_model('unknown_type')
            assert unknown_model == 'gpt-4o-mini'

    def test_get_configured_model_with_config(self, app):
        """Test configurazione modelli custom"""
        with app.app_context():
            app.config['OPENAI_ANALYSIS_MODEL'] = 'gpt-4'
            app.config['OPENAI_CHAT_MODEL'] = 'gpt-3.5-turbo'
            
            from services.ai import get_configured_model
            
            analysis_model = get_configured_model('analysis')
            assert analysis_model == 'gpt-4'
            
            chat_model = get_configured_model('chat')
            assert chat_model == 'gpt-3.5-turbo'

    def test_ai_service_environment_variables(self, app):
        """Test variabili ambiente per AI services"""
        with app.app_context():
            # Test that environment variables are loaded (they exist in real environment)
            from services.ai import OPENAI_API_KEY, PERPLEXITY_API_KEY
            
            # Just verify they're not None (real keys are already set)
            assert OPENAI_API_KEY is not None
            assert len(OPENAI_API_KEY) > 0
            
            # Perplexity key might not be set in test environment
            # So we just check it's either None or a string
            assert PERPLEXITY_API_KEY is None or isinstance(PERPLEXITY_API_KEY, str)