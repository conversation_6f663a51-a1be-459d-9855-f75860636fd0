"""
Database Consistency Helpers per Test Unitari
==============================================

Helper functions per gestire le inconsistenze tra modelli e database nei test.
Fornisce utilities per verificare la consistenza e creare dati di test sicuri.
"""

import pytest
from sqlalchemy import inspect
from extensions import db
import logging

logger = logging.getLogger(__name__)


class DBConsistencyError(Exception):
    """Eccezione per problemi di consistenza database/modelli"""
    pass


class DBConsistencyHelper:
    """Helper class per verificare e gestire consistenza DB/modelli"""
    
    def __init__(self, app_context=None):
        """Inizializza helper con contesto Flask app"""
        self.inspector = inspect(db.engine)
        self._table_cache = {}
        self._model_cache = {}
    
    def get_table_columns(self, table_name):
        """O<PERSON><PERSON> le colonne reali di una tabella dal database"""
        if table_name not in self._table_cache:
            try:
                columns = {}
                for col_info in self.inspector.get_columns(table_name):
                    col_name = col_info['name']
                    columns[col_name] = {
                        'type': str(col_info['type']),
                        'nullable': col_info['nullable'],
                        'default': col_info.get('default'),
                        'primary_key': False  # Will be updated if needed
                    }
                
                # Get primary key info
                pk_info = self.inspector.get_pk_constraint(table_name)
                if pk_info and 'constrained_columns' in pk_info:
                    for col_name in pk_info['constrained_columns']:
                        if col_name in columns:
                            columns[col_name]['primary_key'] = True
                
                self._table_cache[table_name] = columns
                
            except Exception as e:
                logger.warning(f"Could not get columns for table {table_name}: {e}")
                self._table_cache[table_name] = {}
                
        return self._table_cache[table_name]
    
    def get_model_columns(self, model_class):
        """Ottiene le colonne definite in un modello SQLAlchemy"""
        table_name = model_class.__tablename__
        
        if table_name not in self._model_cache:
            columns = {}
            
            if hasattr(model_class, '__table__'):
                for col_name, column in model_class.__table__.columns.items():
                    columns[col_name] = {
                        'type': str(column.type),
                        'nullable': column.nullable,
                        'primary_key': column.primary_key,
                        'foreign_key': bool(column.foreign_keys),
                        'unique': column.unique,
                        'default': str(column.default) if column.default else None
                    }
            
            self._model_cache[table_name] = columns
        
        return self._model_cache[table_name]
    
    def verify_model_db_consistency(self, model_class):
        """Verifica che un modello sia consistente con il database"""
        table_name = model_class.__tablename__
        
        # Check if table exists
        if not self.inspector.has_table(table_name):
            raise DBConsistencyError(f"Table '{table_name}' does not exist in database")
        
        # Get columns from both model and database
        model_cols = self.get_model_columns(model_class)
        db_cols = self.get_table_columns(table_name)
        
        model_col_names = set(model_cols.keys())
        db_col_names = set(db_cols.keys())
        
        # Check for missing columns in database
        missing_in_db = model_col_names - db_col_names
        if missing_in_db:
            raise DBConsistencyError(
                f"Model {model_class.__name__} has columns not in database: {missing_in_db}"
            )
        
        # Check for extra columns in database (warning only)
        extra_in_db = db_col_names - model_col_names
        if extra_in_db:
            logger.warning(
                f"Database table '{table_name}' has extra columns not in model: {extra_in_db}"
            )
        
        return True
    
    def get_safe_model_fields(self, model_class):
        """Ottiene solo i campi del modello che esistono nel database"""
        table_name = model_class.__tablename__
        
        if not self.inspector.has_table(table_name):
            return {}
        
        model_cols = self.get_model_columns(model_class)
        db_cols = self.get_table_columns(table_name)
        
        # Return only fields that exist in both model and database
        safe_fields = {}
        for col_name in model_cols:
            if col_name in db_cols:
                safe_fields[col_name] = model_cols[col_name]
        
        return safe_fields
    
    def create_safe_instance_data(self, model_class, **override_data):
        """Crea dati sicuri per istanziare un modello, rimuovendo campi problematici"""
        safe_fields = self.get_safe_model_fields(model_class)
        safe_data = {}
        
        # Include only safe fields from override_data
        for field_name, value in override_data.items():
            if field_name in safe_fields:
                safe_data[field_name] = value
            else:
                logger.warning(
                    f"Skipping field '{field_name}' for {model_class.__name__} - not safe"
                )
        
        return safe_data


# Global helper instance
_consistency_helper = None


def get_consistency_helper():
    """Ottiene l'helper di consistenza globale"""
    global _consistency_helper
    if _consistency_helper is None:
        _consistency_helper = DBConsistencyHelper()
    return _consistency_helper


def verify_model_consistency(model_class):
    """Verifica che un modello sia consistente con il database"""
    helper = get_consistency_helper()
    return helper.verify_model_db_consistency(model_class)


def get_safe_fields(model_class):
    """Ottiene campi sicuri per un modello"""
    helper = get_consistency_helper()
    return helper.get_safe_model_fields(model_class)


def create_safe_data(model_class, **data):
    """Crea dati sicuri per un modello"""
    helper = get_consistency_helper()
    return helper.create_safe_instance_data(model_class, **data)


def table_exists(model_class):
    """Verifica se la tabella per un modello esiste"""
    helper = get_consistency_helper()
    return helper.inspector.has_table(model_class.__tablename__)


# Decoratore per test che richiedono consistenza DB
def requires_db_consistency(model_class):
    """Decoratore che salta il test se il modello non è consistente con il DB"""
    def decorator(test_func):
        def wrapper(*args, **kwargs):
            try:
                verify_model_consistency(model_class)
                return test_func(*args, **kwargs)
            except DBConsistencyError as e:
                pytest.skip(f"Skipping test due to DB inconsistency: {e}")
        
        wrapper.__name__ = test_func.__name__
        wrapper.__doc__ = test_func.__doc__
        return wrapper
    return decorator


# Decoratore per test che richiedono l'esistenza della tabella
def requires_table_exists(model_class):
    """Decoratore che salta il test se la tabella non esiste"""
    def decorator(test_func):
        def wrapper(*args, **kwargs):
            if not table_exists(model_class):
                pytest.skip(f"Skipping test - table {model_class.__tablename__} does not exist")
            return test_func(*args, **kwargs)
        
        wrapper.__name__ = test_func.__name__
        wrapper.__doc__ = test_func.__doc__
        return wrapper
    return decorator


# Assertion helpers
def assert_model_db_consistency(model_class):
    """Assertion che verifica la consistenza modello/database"""
    try:
        verify_model_consistency(model_class)
    except DBConsistencyError as e:
        pytest.fail(f"Model {model_class.__name__} is not consistent with database: {e}")


def assert_table_exists(model_class):
    """Assertion che verifica l'esistenza della tabella"""
    if not table_exists(model_class):
        pytest.fail(f"Table {model_class.__tablename__} does not exist in database")


# Fixture helper
@pytest.fixture(scope='session')
def db_consistency_helper():
    """Fixture che fornisce l'helper di consistenza"""
    return get_consistency_helper()


@pytest.fixture(scope='function') 
def safe_model_data():
    """Fixture factory per creare dati sicuri per i modelli"""
    def _create_safe_data(model_class, **data):
        return create_safe_data(model_class, **data)
    return _create_safe_data