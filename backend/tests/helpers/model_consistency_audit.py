#!/usr/bin/env python3
"""
Model-Database Consistency Audit Tool
=====================================

Script per identificare e prioritizzare i problemi di consistenza
tra modelli SQLAlchemy e schema database.
"""

from app import create_app
from tests.helpers.db_consistency import scan_all_models_consistency, print_consistency_report

def prioritize_issues(results):
    """Prioritizza i problemi per importanza"""
    
    critical_models = [
        'User', 'Project', 'Task', 'TimeOffRequest', 'Client', 'Contact',
        'Contract', 'Proposal', 'TimesheetEntry', 'Invoice', 'MonthlyTimesheet'
    ]
    
    high_priority = []
    medium_priority = []
    low_priority = []
    
    for model_name, result in results.items():
        if result['status'] == 'ERROR':
            if model_name in critical_models:
                high_priority.append((model_name, result))
            elif 'has columns not in' in result['message']:
                medium_priority.append((model_name, result))
            else:
                low_priority.append((model_name, result))
    
    return high_priority, medium_priority, low_priority

def main():
    """Main audit function"""
    app = create_app()
    
    with app.app_context():
        print("🔍 COMPREHENSIVE MODEL-DATABASE CONSISTENCY AUDIT")
        print("=" * 60)
        
        results = scan_all_models_consistency(strict_mode=True)
        
        high, medium, low = prioritize_issues(results)
        
        print(f"\n📊 PRIORITY BREAKDOWN:")
        print(f"   🚨 HIGH PRIORITY (Core Business Models): {len(high)}")
        print(f"   ⚠️  MEDIUM PRIORITY (Field Mismatches): {len(medium)}")
        print(f"   ℹ️  LOW PRIORITY (Other Issues): {len(low)}")
        
        if high:
            print(f"\n🚨 HIGH PRIORITY FIXES NEEDED:")
            print("-" * 40)
            for model_name, result in high:
                print(f"\n❌ {model_name}:")
                # Show only field mismatch lines
                lines = result['message'].split('\n')
                for line in lines:
                    if 'has columns not in' in line or 'Database table' in line:
                        print(f"   {line.strip()}")
        
        print(f"\n💡 RECOMMENDATION:")
        if high:
            print(f"   1. Fix HIGH PRIORITY models first ({len(high)} models)")
            print(f"   2. These affect core business functionality")
            print(f"   3. May cause production errors like TimeOffRequest")
        else:
            print(f"   ✅ All core business models are consistent!")
            print(f"   📝 Consider fixing medium priority field mismatches when time permits")
        
        print(f"\n🛠️  TOOLS AVAILABLE:")
        print(f"   - tests/helpers/db_consistency.py: Updated consistency checker")
        print(f"   - This script: Re-run anytime to check progress")
        print(f"   - scan_all_models_consistency(): Programmatic access")

if __name__ == "__main__":
    main()