"""
Critical Model-Database Consistency Checker
==========================================

Verifica SOLO i problemi che causano errori di produzione,
non tutte le piccole differenze di schema.
"""

from app import create_app
from models import TimeOffRequest, User, Project, TimesheetEntry
from tests.helpers.db_consistency import get_consistency_helper, DBConsistencyError

def check_critical_api_fields():
    """Verifica che i campi usati dalle API esistano nei modelli"""
    
    critical_checks = [
        {
            'model': TimeOffRequest,
            'api_fields': ['submission_date', 'type', 'approved_at', 'reason'],
            'description': 'Time-off requests API fields'
        },
        {
            'model': User,
            'api_fields': ['id', 'username', 'email', 'first_name', 'last_name'],
            'description': 'User authentication fields'
        },
        {
            'model': Project,
            'api_fields': ['id', 'name', 'budget', 'status', 'created_at'],
            'description': 'Project management fields'
        },
        {
            'model': TimesheetEntry,
            'api_fields': ['id', 'user_id', 'project_id', 'hours', 'date'],
            'description': 'Timesheet tracking fields'
        }
    ]
    
    issues_found = []
    
    for check in critical_checks:
        model_class = check['model']
        api_fields = check['api_fields']
        description = check['description']
        
        print(f"🔍 Checking {model_class.__name__}: {description}")
        
        # Test model instance
        instance = model_class()
        
        missing_fields = []
        for field in api_fields:
            if not hasattr(instance, field):
                missing_fields.append(field)
        
        if missing_fields:
            issues_found.append({
                'model': model_class.__name__,
                'missing_fields': missing_fields,
                'description': description
            })
            print(f"  ❌ MISSING: {missing_fields}")
        else:
            print(f"  ✅ All fields present")
    
    return issues_found

def verify_timeoff_fix():
    """Verifica specifica per il fix di TimeOffRequest"""
    print(f"\n🎯 SPECIFIC VERIFICATION: TimeOffRequest Fix")
    print("-" * 50)
    
    instance = TimeOffRequest()
    
    # Test the specific fields that were causing the production error
    checks = [
        ('submission_date', 'Field accessed in API line 73, 98, 262'),
        ('type', 'Field used in API filtering'),
        ('approved_at', 'Field used in API response'),
        ('reason', 'Field used in API creation')
    ]
    
    all_ok = True
    for field, description in checks:
        if hasattr(instance, field):
            print(f"  ✅ {field}: {description}")
        else:
            print(f"  ❌ {field}: MISSING - {description}")
            all_ok = False
    
    if all_ok:
        print(f"\n🎉 TimeOffRequest fix is SUCCESSFUL!")
        print(f"   Production error should be resolved.")
    else:
        print(f"\n🚨 TimeOffRequest fix needs work!")
    
    return all_ok

def main():
    """Main function per critical consistency check"""
    app = create_app()
    
    with app.app_context():
        print("🚨 CRITICAL MODEL-DATABASE CONSISTENCY CHECK")
        print("=" * 60)
        print("Checking ONLY fields that cause production errors...")
        
        # 1. Verify TimeOffRequest fix specifically
        timeoff_ok = verify_timeoff_fix()
        
        # 2. Check other critical API fields
        print(f"\n🔍 CHECKING OTHER CRITICAL API FIELDS")
        print("-" * 50)
        critical_issues = check_critical_api_fields()
        
        # 3. Summary
        print(f"\n📊 SUMMARY")
        print("-" * 20)
        
        if timeoff_ok and not critical_issues:
            print("✅ ALL CRITICAL CHECKS PASSED!")
            print("   No production-breaking issues detected.")
        else:
            print("❌ CRITICAL ISSUES FOUND:")
            if not timeoff_ok:
                print("   - TimeOffRequest fix incomplete")
            for issue in critical_issues:
                print(f"   - {issue['model']}: missing {issue['missing_fields']}")
        
        print(f"\n💡 NOTE:")
        print(f"   This check focuses on production-critical fields only.")
        print(f"   Other schema differences may exist but are non-critical.")

if __name__ == "__main__":
    main()