# Test Migration TODO

## Status Test Suite

### ✅ WORKING (Core Infrastructure)
- `tests/integration/test_auth.py` - Core auth functionality
- `tests/integration/test_session.py` - Session management  
- `tests/api/test_timeoff_requests_bug_detection.py` - Bug detection system
- `tests/api/test_api_model_consistency.py` - API-Model consistency validation
- `tests/api/test_bug_simulation.py` - Bug prevention validation

### 🔧 NEEDS MIGRATION (Auth Pattern)
These tests use old `url_for('auth.login')` pattern and need migration to API-based auth:

- `tests/integration/test_admin.py` - Admin access tests
- `tests/integration/test_auth_password_reset.py` - Password reset flow
- `tests/integration/test_rbac.py` - Role-based access control
- `tests/integration/test_security.py` - Security validation
- `tests/integration/test_communication.py` - Communication features

### ❌ FAILING (Various Issues)
- `tests/integration/test_ceo_ai_services.py` - Missing pytest-asyncio 
- `tests/integration/test_hr_ai_services.py` - Missing pytest-asyncio
- `tests/integration/test_project_workflows.py` - Database/setup issues

## Migration Strategy

### Phase 1: Fix Core Auth Pattern (IMMEDIATE)
1. Update all tests using `url_for('auth.login')` to use API endpoints
2. Pattern: `client.post('/api/auth/login', json={'username': x, 'password': y})`
3. Files to fix:
   - test_admin.py ✅ STARTED
   - test_auth_password_reset.py
   - test_rbac.py  
   - test_security.py

### Phase 2: Fix Async Tests (MEDIUM PRIORITY)
1. Add pytest-asyncio to test dependencies
2. Update async test patterns
3. Files to fix:
   - test_ceo_ai_services.py
   - test_hr_ai_services.py

### Phase 3: Database Setup Issues (MEDIUM PRIORITY)
1. Fix missing table errors (company_invoicing_settings)
2. Fix SQLAlchemy warnings
3. Files to fix:
   - test_project_workflows.py
   - Various tests with table issues

### Phase 4: Test Cleanup (LOW PRIORITY)
1. Remove obsolete tests for non-existent models
2. Update deprecated SQLAlchemy patterns
3. Fix pytest warnings

## Quick Fixes Applied

### Removed Obsolete Tests
- ❌ `test_deal_model.py` - Model doesn't exist
- ❌ `test_invoiceitem_model.py` - Model doesn't exist  
- ❌ `test_lead_model.py` - Model doesn't exist
- ❌ `test_opportunity_model.py` - Model doesn't exist

### Fixed Core Infrastructure
- ✅ TimeOffRequest model-API consistency issues
- ✅ Auth endpoint naming in core tests
- ✅ Bug detection and prevention system

## Test Statistics
- Total tests: ~1073
- Core working: ~69 passing
- Need auth migration: ~20 failing
- Other issues: ~3 errors

## Recommendation
Focus on Phase 1 (auth pattern migration) to get majority of tests passing quickly.
This will provide stable foundation for continued development.