"""
Test for automatic API-Model consistency validation.
Scans API code to find field/relationship usage and validates against models.
"""

import pytest
import re
import os
from pathlib import Path


class TestAPIModelConsistency:
    """Test suite for validating API code against model definitions"""
    
    def _extract_model_usage_from_api(self, api_file_path, model_name):
        """Extract field and relationship usage from API file"""
        try:
            with open(api_file_path, 'r') as f:
                content = f.read()
        except FileNotFoundError:
            return [], []
        
        # Patterns to find model field/relationship usage
        # Examples: req.field_name, model.relationship.field, model.field
        variable_names = [
            'req', 'request', model_name.lower(), 'time_off_request', 
            'time_off', 'instance', 'obj', 'item'
        ]
        
        fields_found = set()
        relationships_found = set()
        
        for var_name in variable_names:
            # Pattern: var.field_name
            field_pattern = rf'{var_name}\.(\w+)'
            field_matches = re.findall(field_pattern, content)
            
            # Pattern: var.relationship.field (indicates relationship usage)
            relationship_pattern = rf'{var_name}\.(\w+)\.\w+'
            rel_matches = re.findall(relationship_pattern, content)
            
            fields_found.update(field_matches)
            relationships_found.update(rel_matches)
        
        # Filter out common non-field words (Flask/Python methods, not model fields)
        common_words = {
            'query', 'filter', 'get', 'all', 'first', 'count', 'join', 
            'isoformat', 'append', 'add', 'delete', 'commit', 'rollback',
            'json', 'data', 'success', 'message', 'status_code', 'args',
            'get_json', 'method', 'headers', 'form', 'files', 'values',
            'items', 'keys', 'strip', 'lower', 'upper', 'split', 'replace'
        }
        
        fields_found = [f for f in fields_found if f not in common_words]
        relationships_found = [r for r in relationships_found if r not in common_words]
        
        return list(fields_found), list(relationships_found)
    
    def test_timeoff_request_api_model_consistency(self, app):
        """Test TimeOffRequest API usage against model definition"""
        with app.app_context():
            from models import TimeOffRequest
            
            # Path to TimeOffRequest API
            api_path = Path(__file__).parent.parent.parent / 'blueprints' / 'api' / 'timeoff_requests.py'
            
            # Extract usage from API code
            fields_used, relationships_used = self._extract_model_usage_from_api(
                str(api_path), 'TimeOffRequest'
            )
            
            print(f"📋 Fields found in API: {sorted(fields_used)}")
            print(f"📋 Relationships found in API: {sorted(relationships_used)}")
            
            # Test model instance
            instance = TimeOffRequest()
            
            # Check fields
            missing_fields = []
            for field in fields_used:
                if not hasattr(instance, field):
                    missing_fields.append(field)
            
            # Check relationships
            missing_relationships = []
            for relationship in relationships_used:
                if not hasattr(instance, relationship):
                    missing_relationships.append(relationship)
            
            # Report issues
            issues = []
            if missing_fields:
                issues.append(f"Missing fields: {missing_fields}")
            if missing_relationships:
                issues.append(f"Missing relationships: {missing_relationships}")
            
            if issues:
                issue_text = "; ".join(issues)
                print(f"🐛 AUTOMATIC BUG DETECTION: {issue_text}")
                
                # This would have caught the requester vs user bug!
                assert False, (f"API-Model consistency check failed: {issue_text}. "
                             f"The API code uses fields/relationships that don't exist in the model.")
            
            print("✅ Automatic API-Model consistency check passed")
    
    def test_simulate_requester_bug(self, app):
        """Simulate the requester vs user bug to prove test catches it"""
        with app.app_context():
            from models import TimeOffRequest
            
            # Simulate what would happen if we had 'requester' instead of 'user'
            instance = TimeOffRequest()
            
            # API expects 'user' relationship (for req.user.first_name)
            api_expected_relationships = ['user']
            
            # Simulate if model had 'requester' instead
            # (We'll pretend the model doesn't have 'user')
            missing_relationships = []
            for rel in api_expected_relationships:
                if not hasattr(instance, rel):
                    missing_relationships.append(rel)
            
            # This should NOT fail now since we fixed it
            if missing_relationships:
                pytest.fail(f"Model missing relationships: {missing_relationships}")
            
            print("✅ Current model correctly has 'user' relationship")
    
    def test_api_pattern_detection(self):
        """Test our pattern detection logic"""
        # Sample API code to test pattern detection
        sample_api_code = '''
        req.submission_date.isoformat()
        time_off_request.user.first_name
        req.status == 'approved'
        instance.approved_by
        obj.type
        request.notes
        '''
        
        # Extract patterns
        fields_found = set()
        relationships_found = set()
        
        variable_names = ['req', 'time_off_request', 'instance', 'obj', 'request']
        
        for var_name in variable_names:
            # Pattern: var.field_name
            field_pattern = rf'{var_name}\.(\w+)'
            field_matches = re.findall(field_pattern, sample_api_code)
            
            # Pattern: var.relationship.field  
            relationship_pattern = rf'{var_name}\.(\w+)\.\w+'
            rel_matches = re.findall(relationship_pattern, sample_api_code)
            
            fields_found.update(field_matches)
            relationships_found.update(rel_matches)
        
        print(f"📋 Pattern test - Fields found: {sorted(fields_found)}")
        print(f"📋 Pattern test - Relationships found: {sorted(relationships_found)}")
        
        # Should find the expected patterns
        assert 'submission_date' in fields_found
        assert 'status' in fields_found
        assert 'approved_by' in fields_found
        assert 'type' in fields_found
        assert 'notes' in fields_found
        assert 'user' in relationships_found
        
        print("✅ Pattern detection working correctly")