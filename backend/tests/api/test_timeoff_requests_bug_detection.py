"""
Test per detection automatica di bug su TimeOffRequest API.
Questo test avrebbe catturato il bug submission_date mancante.
"""

import pytest
import json
from datetime import datetime, date, timedelta
from flask import url_for

from models import TimeOffRequest, User, Department
from extensions import db
from utils.api_utils import api_response


class TestTimeOffRequestsBugDetection:
    """Test per catturare bug model-API mismatch"""

    @pytest.fixture
    def setup_time_off_data(self, db_session):
        """Setup dati per test time-off requests API"""
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        # Crea dipartimento
        dept = Department(
            name=f'HR_Test_{unique_id}',
            description='HR Department for testing'
        )
        db_session.add(dept)
        db_session.commit()
        
        # Crea utenti
        employee = User(
            username=f'employee_timeoff_{unique_id}',
            email=f'employee.timeoff.{unique_id}@test.com',
            first_name='Employee',
            last_name='Test',
            role='employee',
            department_id=dept.id
        )
        employee.set_password('password123')
        
        manager = User(
            username=f'manager_timeoff_{unique_id}',
            email=f'manager.timeoff.{unique_id}@test.com',
            first_name='Manager',
            last_name='Test',
            role='manager',
            department_id=dept.id
        )
        manager.set_password('password123')
        
        db_session.add_all([employee, manager])
        db_session.commit()
        
        # Crea time-off request per test
        time_off = TimeOffRequest(
            user_id=employee.id,
            type='vacation',
            start_date=date.today() + timedelta(days=7),
            end_date=date.today() + timedelta(days=10),
            reason='Family vacation',
            status='pending'
        )
        db_session.add(time_off)
        db_session.commit()
        
        return {
            'employee': employee,
            'manager': manager,
            'time_off': time_off,
            'department': dept
        }

    def test_time_off_api_field_validation(self, app, client, setup_time_off_data):
        """Test che verifica tutti i campi API vs Model per catturare field mismatch"""
        with app.app_context():
            data = setup_time_off_data
            
            # Login come manager per vedere tutte le richieste
            login_data = {
                'username': data['manager'].username,
                'password': 'password123'
            }
            client.post('/api/auth/login', json=login_data)
            
            # Test GET /api/time-off-requests/ - Questo dovrebbe fallire se submission_date è missing
            response = client.get('/api/time-off-requests/')
            
            # Questo test cattura il bug: se submission_date non esiste nel modello
            # ma è usato nell'API, l'API dovrebbe fallire
            if response.status_code != 200:
                # BUG DETECTED: L'API fallisce
                assert "submission_date" in str(response.data), "API error should mention submission_date field"
                print(f"🐛 BUG DETECTED: {response.data}")
                return
            
            # Se l'API non fallisce, verifica che i dati siano corretti
            assert response.status_code == 200
            data_response = response.get_json()
            assert data_response['success'] is True
            
            # Verifica che ogni time-off request abbia tutti i campi expected
            if 'data' in data_response and data_response['data'] and len(data_response['data']) > 0:
                # Handle both list and dict responses
                if isinstance(data_response['data'], list):
                    time_off_item = data_response['data'][0]
                elif isinstance(data_response['data'], dict) and 'items' in data_response['data']:
                    if data_response['data']['items']:
                        time_off_item = data_response['data']['items'][0]
                    else:
                        print("No time-off requests found in paginated response")
                        return
                else:
                    print(f"Unexpected data format: {type(data_response['data'])}")
                    return
                
                # Test field consistency: questi campi devono esistere nel modello
                expected_fields = [
                    'id', 'user_id', 'type', 'start_date', 'end_date', 
                    'reason', 'status', 'created_at'
                ]
                
                for field in expected_fields:
                    assert field in time_off_item, f"Expected field '{field}' missing from API response"
                
                # Test submission_date specifico - questo può essere None ma non deve causare errori
                if 'submission_date' in time_off_item:
                    # Se è presente, deve essere un formato valido o None
                    submission_date = time_off_item['submission_date']
                    if submission_date is not None:
                        # Verifica che sia un formato datetime valido
                        try:
                            datetime.fromisoformat(submission_date.replace('Z', '+00:00'))
                        except ValueError:
                            assert False, f"Invalid submission_date format: {submission_date}"

    def test_time_off_model_vs_api_consistency(self, app, db_session, setup_time_off_data):
        """Test che verifica consistency tra Model fields e API response"""
        with app.app_context():
            data = setup_time_off_data
            time_off = data['time_off']
            
            # Get model attributes
            model_attributes = [attr for attr in dir(time_off) 
                              if not attr.startswith('_') 
                              and not callable(getattr(time_off, attr))
                              and attr not in ['metadata', 'query', 'query_class']]
            
            print(f"📊 TimeOffRequest model attributes: {model_attributes}")
            
            # Verifica che submission_date sia effettivamente presente nel modello
            has_submission_date = hasattr(time_off, 'submission_date')
            print(f"🔍 Model has submission_date: {has_submission_date}")
            
            if not has_submission_date:
                print("🐛 BUG CONFIRMED: TimeOffRequest model missing submission_date field")
                print("   This explains why the API fails when trying to access req.submission_date")
                
                # Questo test documenterebbe il bug e la fix necessaria
                assert False, ("Model-API mismatch: TimeOffRequest model is missing 'submission_date' field "
                             "but API code in timeoff_requests.py tries to access it. "
                             "Fix: Add submission_date field to TimeOffRequest model "
                             "or remove submission_date usage from API.")

    def test_time_off_api_error_handling(self, app, client, setup_time_off_data):
        """Test che API gracefully handle missing model fields"""
        with app.app_context():
            data = setup_time_off_data
            
            # Login come employee
            login_data = {
                'username': data['employee'].username,
                'password': 'password123'
            }
            client.post('/api/auth/login', json=login_data)
            
            # Test POST create new time-off request
            new_request_data = {
                'type': 'leave',  # Use valid type: vacation, leave, smartworking
                'start_date': (date.today() + timedelta(days=14)).isoformat(),
                'end_date': (date.today() + timedelta(days=16)).isoformat(),
                'reason': 'Medical appointment'
            }
            
            response = client.post('/api/time-off-requests/', 
                                 json=new_request_data,
                                 content_type='application/json')
            
            # API dovrebbe gestire gracefully missing fields
            if response.status_code == 500:
                error_data = response.get_json()
                if error_data and 'submission_date' in str(error_data):
                    print("🐛 BUG DETECTED in POST: submission_date field issue")
                    assert False, f"API POST fails due to submission_date: {error_data}"
            
            # Se non fallisce, verifica che la response sia corretta
            if response.status_code not in [200, 201]:
                error_data = response.get_json()
                print(f"API Error ({response.status_code}): {error_data}")
            
            assert response.status_code in [200, 201], f"Unexpected status: {response.status_code}, data: {response.get_json()}"

    def test_time_off_api_comprehensive_field_check(self, app):
        """Test completo per verificare che tutti i field dell'API esistano nel modello"""
        with app.app_context():
            # Import del modello
            from models import TimeOffRequest
            
            # Crea un'istanza temporanea per test
            temp_instance = TimeOffRequest()
            
            # Campi che l'API cerca di utilizzare (da grep della codebase)
            api_expected_fields = [
                'id', 'user_id', 'type', 'start_date', 'end_date', 
                'reason', 'status', 'approved_by', 'approved_at', 
                'notes', 'created_at', 'updated_at', 'submission_date'
            ]
            
            # Relationships che l'API cerca di utilizzare (req.user.first_name, etc.)
            api_expected_relationships = [
                'user',  # Usado in req.user.first_name, req.user.id, etc.
                'approver'  # Potentially used for approved_by relationship
            ]
            
            missing_fields = []
            missing_relationships = []
            
            # Check fields
            for field in api_expected_fields:
                if not hasattr(temp_instance, field):
                    missing_fields.append(field)
            
            # Check relationships  
            for relationship in api_expected_relationships:
                if not hasattr(temp_instance, relationship):
                    missing_relationships.append(relationship)
                    
            issues = []
            if missing_fields:
                issues.append(f"Missing fields: {missing_fields}")
            if missing_relationships:
                issues.append(f"Missing relationships: {missing_relationships}")
            
            if issues:
                issue_text = "; ".join(issues)
                print(f"🐛 BUG DETECTED: TimeOffRequest model issues: {issue_text}")
                print("   These are used in API but don't exist in model")
                
                # This test would fail and tell us exactly what to fix
                assert False, (f"Model-API mismatch: TimeOffRequest model has issues: {issue_text}. "
                             f"Fix the model to match API expectations.")
            
            print("✅ All API expected fields and relationships exist in TimeOffRequest model")


class TestModelDrivenAPIValidation:
    """Test framework per validare automatically API vs Model consistency"""
    
    def test_all_models_api_consistency(self, app):
        """Test generico che verifica consistency per tutti i modelli principali"""
        with app.app_context():
            # Lista modelli critici da testare
            from models import (
                TimeOffRequest, Project, Task, User, Client, 
                Contract, Proposal, TimesheetEntry
            )
            
            critical_models = [
                ('TimeOffRequest', TimeOffRequest),
                ('Project', Project),
                ('Task', Task),
                ('User', User),
                ('Client', Client),
                ('Contract', Contract),
                ('Proposal', Proposal),
                ('TimesheetEntry', TimesheetEntry)
            ]
            
            api_field_usage = {
                'TimeOffRequest': ['submission_date'],  # Known issue
                'Project': ['created_at', 'updated_at'],
                'Task': ['created_at', 'updated_at'],
                # Add more as we find them
            }
            
            issues_found = []
            
            for model_name, model_class in critical_models:
                if model_name in api_field_usage:
                    temp_instance = model_class()
                    expected_fields = api_field_usage[model_name]
                    
                    for field in expected_fields:
                        if not hasattr(temp_instance, field):
                            issues_found.append(f"{model_name}.{field}")
            
            if issues_found:
                print(f"🐛 MODEL-API CONSISTENCY ISSUES DETECTED:")
                for issue in issues_found:
                    print(f"   - Missing field: {issue}")
                
                # Questo test ci avrebbe avvertito del problema submission_date
                assert False, f"Model-API field mismatches found: {issues_found}"
            
            print("✅ All critical models pass API consistency check")