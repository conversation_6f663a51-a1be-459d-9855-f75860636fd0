#!/usr/bin/env python3
"""
Database Health Check per Test Unitari
======================================

Script completo per verificare la consistenza tra modelli SQLAlchemy e database,
specificamente progettato per supportare i test unitari.

Identifica:
- Modelli senza tabelle corrispondenti
- Colonne mancanti nei modelli o nel database
- Discrepanze di tipo e constraint
- Problemi di foreign key
- Raccomandazioni per i test

Uso:
    python scripts/test_db_health_check.py
    python scripts/test_db_health_check.py --fix-suggestions
    python scripts/test_db_health_check.py --models-only CEO,HR_Assistant
"""

import sys
import os
import argparse
from datetime import datetime
import json

# Add backend directory to path
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, backend_dir)

from app import create_app
from extensions import db
from sqlalchemy import inspect
import importlib


class TestDBHealthChecker:
    """Health checker per database e modelli con focus sui test"""
    
    def __init__(self, app_context=None):
        """Inizializza il checker con contesto Flask"""
        if app_context is None:
            self.app = create_app()
            self.app_context = self.app.app_context()
            self.app_context.push()
        else:
            self.app_context = app_context
            
        self.inspector = inspect(db.engine)
        self.issues = []
        self.recommendations = []
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if hasattr(self, 'app_context'):
            self.app_context.pop()
    
    def get_all_models(self):
        """Ottiene tutti i modelli SQLAlchemy registrati"""
        models = {}
        
        # Import all model modules
        model_modules = [
            'user', 'communication', 'hr', 'projects', 'timesheets', 
            'crm', 'invoicing', 'business', 'content', 'system',
            'funding', 'performance', 'portfolio', 'certifications', 
            'ceo', 'hr_assistant'
        ]
        
        for module_name in model_modules:
            try:
                module = importlib.import_module(f'models_split.{module_name}')
                
                # Find all SQLAlchemy models in the module
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    
                    if (hasattr(attr, '__tablename__') and 
                        hasattr(attr, '__table__') and
                        hasattr(attr, '__module__')):
                        
                        table_name = attr.__tablename__
                        models[table_name] = {
                            'class_name': attr_name,
                            'module': module_name,
                            'model_class': attr,
                            'columns': self._get_model_columns(attr)
                        }
                        
            except ImportError as e:
                self.issues.append(f"Failed to import models_split.{module_name}: {e}")
                
        return models
    
    def _get_model_columns(self, model_class):
        """Estrae informazioni sulle colonne da un modello SQLAlchemy"""
        columns = {}
        
        for col_name, column in model_class.__table__.columns.items():
            col_info = {
                'type': str(column.type),
                'python_type': column.type.python_type.__name__ if hasattr(column.type, 'python_type') else 'unknown',
                'nullable': column.nullable,
                'primary_key': column.primary_key,
                'foreign_key': bool(column.foreign_keys),
                'unique': column.unique,
                'default': str(column.default) if column.default else None,
                'server_default': str(column.server_default) if column.server_default else None
            }
            
            # Get foreign key information
            if column.foreign_keys:
                fk = list(column.foreign_keys)[0]
                col_info['foreign_key_target'] = f"{fk.column.table.name}.{fk.column.name}"
                
            columns[col_name] = col_info
            
        return columns
    
    def get_db_tables_info(self):
        """Ottiene informazioni su tutte le tabelle dal database"""
        tables = {}
        
        for table_name in self.inspector.get_table_names():
            columns = {}
            
            for col_info in self.inspector.get_columns(table_name):
                col_name = col_info['name']
                columns[col_name] = {
                    'type': str(col_info['type']),
                    'nullable': col_info['nullable'],
                    'default': col_info.get('default'),
                    'primary_key': False,  # Will be updated below
                    'foreign_key': False,  # Will be updated below
                    'unique': False  # Will be updated below
                }
            
            # Get primary keys
            pk_info = self.inspector.get_pk_constraint(table_name)
            if pk_info and 'constrained_columns' in pk_info:
                for col_name in pk_info['constrained_columns']:
                    if col_name in columns:
                        columns[col_name]['primary_key'] = True
            
            # Get foreign keys
            fk_info = self.inspector.get_foreign_keys(table_name)
            for fk in fk_info:
                for col_name in fk['constrained_columns']:
                    if col_name in columns:
                        columns[col_name]['foreign_key'] = True
                        ref_table = fk['referred_table']
                        ref_cols = fk['referred_columns']
                        if ref_cols:
                            columns[col_name]['foreign_key_target'] = f"{ref_table}.{ref_cols[0]}"
            
            # Get unique constraints
            unique_info = self.inspector.get_unique_constraints(table_name)
            for unique in unique_info:
                for col_name in unique['column_names']:
                    if col_name in columns:
                        columns[col_name]['unique'] = True
            
            tables[table_name] = {
                'columns': columns,
                'indexes': self.inspector.get_indexes(table_name),
                'foreign_keys': fk_info
            }
            
        return tables
    
    def check_model_db_consistency(self, models_filter=None):
        """Verifica la consistenza tra modelli e database"""
        print("🔍 DATABASE HEALTH CHECK PER TEST UNITARI")
        print("=" * 60)
        
        # Get model and database information
        models = self.get_all_models()
        db_tables = self.get_db_tables_info()
        
        # Apply filter if specified
        if models_filter:
            filter_modules = [f.lower() for f in models_filter]
            models = {k: v for k, v in models.items() 
                     if any(fm in v['module'].lower() for fm in filter_modules)}
        
        print(f"\n📊 RIASSUNTO:")
        print(f"   - Modelli analizzati: {len(models)}")
        print(f"   - Tabelle nel database: {len(db_tables)}")
        
        # Check each model
        self._check_models_vs_tables(models, db_tables)
        self._check_columns_consistency(models, db_tables)
        self._generate_test_recommendations(models, db_tables)
        
        return self._generate_report()
    
    def _check_models_vs_tables(self, models, db_tables):
        """Verifica l'esistenza di tabelle per ogni modello"""
        print(f"\n📋 VERIFICA ESISTENZA TABELLE:")
        
        model_tables = set(models.keys())
        existing_tables = set(db_tables.keys())
        
        # Models without tables
        missing_tables = model_tables - existing_tables
        if missing_tables:
            print(f"\n❌ MODELLI SENZA TABELLE ({len(missing_tables)}):")
            for table in sorted(missing_tables):
                model_info = models[table]
                print(f"   - {table} (modello: {model_info['class_name']}, modulo: {model_info['module']})")
                self.issues.append({
                    'type': 'missing_table',
                    'table': table,
                    'model': model_info['class_name'],
                    'module': model_info['module'],
                    'severity': 'high'
                })
                
                self.recommendations.append({
                    'type': 'skip_test',
                    'table': table,
                    'reason': 'Table does not exist in database',
                    'action': f'Skip unit tests for {model_info["class_name"]} until table is created'
                })
        
        # Tables without models  
        orphan_tables = existing_tables - model_tables
        if orphan_tables:
            print(f"\n⚠️  TABELLE SENZA MODELLI ({len(orphan_tables)}):")
            for table in sorted(orphan_tables):
                print(f"   - {table}")
                self.issues.append({
                    'type': 'orphan_table', 
                    'table': table,
                    'severity': 'low'
                })
        
        # Tables with models
        common_tables = model_tables & existing_tables
        if common_tables:
            print(f"\n✅ TABELLE CON MODELLI ({len(common_tables)})")
    
    def _check_columns_consistency(self, models, db_tables):
        """Verifica la consistenza delle colonne"""
        print(f"\n🔍 VERIFICA CONSISTENZA COLONNE:")
        
        for table_name in sorted(set(models.keys()) & set(db_tables.keys())):
            model_cols = models[table_name]['columns']
            db_cols = db_tables[table_name]['columns']
            
            model_col_names = set(model_cols.keys())
            db_col_names = set(db_cols.keys())
            
            # Check for missing columns
            missing_in_db = model_col_names - db_col_names
            missing_in_model = db_col_names - model_col_names
            
            if missing_in_db or missing_in_model:
                print(f"\n📋 {table_name} ({models[table_name]['class_name']}):")
                
                if missing_in_model:
                    print(f"   ❌ Colonne nel DB ma non nel modello ({len(missing_in_model)}):")
                    for col in sorted(missing_in_model):
                        col_info = db_cols[col]
                        print(f"      - {col} ({col_info['type']}, nullable={col_info['nullable']})")
                        self.issues.append({
                            'type': 'column_missing_in_model',
                            'table': table_name,
                            'column': col,
                            'severity': 'medium'
                        })
                
                if missing_in_db:
                    print(f"   ❌ Colonne nel modello ma non nel DB ({len(missing_in_db)}):")
                    for col in sorted(missing_in_db):
                        col_info = model_cols[col]
                        print(f"      - {col} ({col_info['type']}, nullable={col_info['nullable']})")
                        self.issues.append({
                            'type': 'column_missing_in_db',
                            'table': table_name,
                            'column': col,
                            'severity': 'high'
                        })
                        
                        self.recommendations.append({
                            'type': 'test_adjustment',
                            'table': table_name,
                            'column': col,
                            'action': f'Remove {col} from test data creation for {table_name}'
                        })
            
            # Check column type consistency for common columns
            common_cols = model_col_names & db_col_names
            for col_name in common_cols:
                model_col = model_cols[col_name]
                db_col = db_cols[col_name]
                
                # Check nullable consistency
                if model_col['nullable'] != db_col['nullable']:
                    self.issues.append({
                        'type': 'nullable_mismatch',
                        'table': table_name,
                        'column': col_name,
                        'model_nullable': model_col['nullable'],
                        'db_nullable': db_col['nullable'],
                        'severity': 'medium'
                    })
    
    def _generate_test_recommendations(self, models, db_tables):
        """Genera raccomandazioni specifiche per i test"""
        print(f"\n💡 RACCOMANDAZIONI PER I TEST:")
        
        safe_models = []
        problematic_models = []
        
        for table_name, model_info in models.items():
            if table_name not in db_tables:
                problematic_models.append((table_name, model_info, 'no_table'))
                continue
                
            model_cols = set(model_info['columns'].keys())
            db_cols = set(db_tables[table_name]['columns'].keys())
            
            if model_cols - db_cols:  # Model has columns not in DB
                problematic_models.append((table_name, model_info, 'missing_columns'))
            else:
                safe_models.append((table_name, model_info))
        
        if safe_models:
            print(f"\n✅ MODELLI SICURI PER I TEST ({len(safe_models)}):")
            for table_name, model_info in safe_models[:10]:  # Show first 10
                print(f"   - {model_info['class_name']} ({table_name})")
            if len(safe_models) > 10:
                print(f"   ... e altri {len(safe_models) - 10}")
        
        if problematic_models:
            print(f"\n⚠️  MODELLI CON PROBLEMI ({len(problematic_models)}):")
            for table_name, model_info, issue_type in problematic_models:
                if issue_type == 'no_table':
                    print(f"   ❌ {model_info['class_name']} - Tabella non esiste")
                elif issue_type == 'missing_columns':
                    print(f"   ⚠️  {model_info['class_name']} - Colonne mancanti nel DB")
    
    def _generate_report(self):
        """Genera un report completo"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_issues': len(self.issues),
                'high_severity': len([i for i in self.issues if i.get('severity') == 'high']),
                'medium_severity': len([i for i in self.issues if i.get('severity') == 'medium']),
                'low_severity': len([i for i in self.issues if i.get('severity') == 'low'])
            },
            'issues': self.issues,
            'recommendations': self.recommendations
        }
        
        print(f"\n📋 RIASSUNTO FINALE:")
        print(f"   - Problemi totali: {report['summary']['total_issues']}")
        print(f"   - Alta priorità: {report['summary']['high_severity']}")
        print(f"   - Media priorità: {report['summary']['medium_severity']}")
        print(f"   - Bassa priorità: {report['summary']['low_severity']}")
        print(f"   - Raccomandazioni: {len(self.recommendations)}")
        
        return report
    
    def save_report(self, filename=None):
        """Salva il report in un file JSON"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_db_health_report_{timestamp}.json"
        
        report = self._generate_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Report salvato in: {filename}")
        return filename


def main():
    """Funzione principale"""
    parser = argparse.ArgumentParser(description='Database Health Check per Test Unitari')
    parser.add_argument('--models-only', help='Analizza solo modelli specifici (es: CEO,HR_Assistant)')
    parser.add_argument('--save-report', help='Salva report in file JSON')
    parser.add_argument('--fix-suggestions', action='store_true', help='Mostra suggerimenti per il fix')
    
    args = parser.parse_args()
    
    models_filter = args.models_only.split(',') if args.models_only else None
    
    try:
        with TestDBHealthChecker() as checker:
            report = checker.check_model_db_consistency(models_filter)
            
            if args.save_report:
                checker.save_report(args.save_report)
            
            if args.fix_suggestions:
                print(f"\n🔧 SUGGERIMENTI PER IL FIX:")
                print("   1. Per modelli senza tabelle: eseguire migrazioni database")
                print("   2. Per colonne mancanti nel DB: aggiornare schema database")
                print("   3. Per colonne mancanti nel modello: aggiornare definizioni modello")
                print("   4. Per i test: usare solo campi esistenti nel database")
                
            return len(report['issues'])
            
    except Exception as e:
        print(f"❌ Errore durante l'esecuzione: {e}")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)