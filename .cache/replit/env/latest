declare -gx REPLIT_SUBCLUSTER=paid
declare -gx NIX_PROFILES='/nix/var/nix/profiles/default /home/<USER>/.nix-profile'
declare -gx PKG_CONFIG_PATH_FOR_TARGET=/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/lib/pkgconfig:/nix/store/191vca5vdxdlr32k2hpzd66mic98930f-openssl-3.0.13-dev/lib/pkgconfig
declare -gx GIT_ASKPASS=replit-git-askpass
declare -gx LD_AUDIT=/nix/store/1blcr36jzjnqs8xcnckviaqh1gky3mhw-replit_rtld_loader-1/rtld_loader.so
declare -gx REPLIT_PYTHONPATH=/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages:/nix/store/wblvmd5y7izx0z10d1w7ga7zc4apjxmb-python3.11-setuptools-75.1.1/lib/python3.11/site-packages
declare -gx REPLIT_BASHRC=/nix/store/5m19p9rqshi95sxcjiq121b6saz8ra31-replit-bashrc/bashrc
declare -gx USER=runner
declare -gx LOCALE_ARCHIVE=/usr/lib/locale/locale-archive
declare -gx REPL_PUBKEYS='{"crosis-ci":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:1":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:latest":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","prod":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:1":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:2":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:3":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:4":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:5":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:latest":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","vault-goval-token":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:1":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:latest":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E="}'
declare -gx POETRY_CACHE_DIR=/home/<USER>/workspace/.cache/pypoetry
declare -gx NIX_LDFLAGS='-L/nix/store/0wyfjxk51chyzvvzv06aklxdcgdi0256-postgresql-15.7-debug/lib -L/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/lib -L/nix/store/xb4h083j02mr2ix7pgj7iawxh2hk100l-postgresql-15.7-lib/lib -L/nix/store/191vca5vdxdlr32k2hpzd66mic98930f-openssl-3.0.13-dev/lib -L/nix/store/vrkxygip635wv4m91b89vbr7hbfih52c-openssl-3.0.13-debug/lib -L/nix/store/gp504m4dvw5k2pdx6pccf1km79fkcwgf-openssl-3.0.13/lib'
declare -gx LIBGL_DRIVERS_PATH=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/lib/dri
declare -gx REPL_LANGUAGE=nix
declare -gx POETRY_CONFIG_DIR=/nix/store/9yrffddwdb6k6i5nxjh8ardgmclc3c5y-poetry-config
declare -gx CFLAGS='-isystem /nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/include -isystem /nix/store/191vca5vdxdlr32k2hpzd66mic98930f-openssl-3.0.13-dev/include'
declare -gx DISPLAY=:0
declare -gx REPLIT_DEV_DOMAIN=5d63f733-8138-476e-bb1d-56c0f7c5cf06-00-28nvn60s8oynr.janeway.replit.dev
declare -gx REPL_OWNER_ID=39305131
declare -gx GIT_EDITOR=replit-git-editor
declare -gx POETRY_PIP_FROM_PATH=1
declare -gx REPLIT_CONTAINER=gcr.io/marine-cycle-160323/repl-base:97433c06ca04972a1619dedd971cb3a93f4a5087
declare -gx LDFLAGS='-L/nix/store/0wyfjxk51chyzvvzv06aklxdcgdi0256-postgresql-15.7-debug/lib -L/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/lib -L/nix/store/xb4h083j02mr2ix7pgj7iawxh2hk100l-postgresql-15.7-lib/lib -L/nix/store/191vca5vdxdlr32k2hpzd66mic98930f-openssl-3.0.13-dev/lib -L/nix/store/vrkxygip635wv4m91b89vbr7hbfih52c-openssl-3.0.13-debug/lib -L/nix/store/gp504m4dvw5k2pdx6pccf1km79fkcwgf-openssl-3.0.13/lib'
declare -gx REPL_ID=5d63f733-8138-476e-bb1d-56c0f7c5cf06
declare -gx XDG_CONFIG_HOME=/home/<USER>/workspace/.config
declare -gx NIX_PS1='\[\033[01;34m\]\w\[\033[00m\]\$ '
declare -gx NIX_CFLAGS_COMPILE='-isystem /nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/include -isystem /nix/store/191vca5vdxdlr32k2hpzd66mic98930f-openssl-3.0.13-dev/include'
declare -gx REPLIT_PYTHON_LD_LIBRARY_PATH=/nix/store/cgac6vgdqpymd47frmlgv85ckhy85zs7-cpplibs/lib:/nix/store/bzk3q2l71qwhycsip23y6rl5n881la4n-zlib-1.3.1/lib:/nix/store/26hcp8h792wl0h52c5r94qakhvk6q717-glib-2.82.1/lib:/nix/store/ikjw6a952jd9wn5k06mkj710xzabssr0-libX11-1.8.10/lib:/nix/store/d5y0sl4vwsb9m99r18zh1mx4fw9y70g6-libXext-1.3.6/lib:/nix/store/zz9384x4kbwanpviwwm5lkh3cvnh4nix-libXinerama-1.1.5/lib:/nix/store/622maagcm5lmh4g21y0ks10zgrkjwq4y-libXcursor-1.2.2/lib:/nix/store/1741axgq503c1r4bzwy1ysp847rsfrf0-libXrandr-1.5.4/lib:/nix/store/mcr8zrlyg2r6idl6ks60858q5q0i6i2a-libXi-1.8.2/lib:/nix/store/y0qjc54zqhb8ksc8iddsadmddkg9vyk9-libXxf86vm-1.1.5/lib
declare -gx HOME=/home/<USER>
declare -gx REPLIT_CLUSTER=janeway
declare -gx REPL_OWNER=danielesabetta
declare -gx REPLIT_PID1_FLAG_PREEVALED_SYSPKGS=1
declare -gx DOCKER_CONFIG=/home/<USER>/workspace/.config/docker
declare -gx XDG_DATA_DIRS=/nix/store/fq20cpxp7bgx9xlc4hq3l99k7r4w4m4p-openssl-3.0.13-doc/share:/nix/store/y2i038ql442nn3iqhx4lxsala5fkqpf4-postgresql-15.7-man/share:/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/share:/nix/store/aydd0lh52si89mxm9rz5xzv3zs6gx8bl-postgresql-15.7-doc/share:/nix/store/sss9pw9l1prvc0a1b01ycrn0xwxxvh3x-openssl-3.0.13-man/share:/nix/store/a04r09wqgcp9kmgfgmpbqmm20b7ffx7w-replit-runtime-path/share
declare -gx HOSTNAME=f3e3aa1fbaa7
declare -gx REPLIT_RIPPKGS_INDICES=/nix/store/l5gcmdp908sji4wchfp8csflhjcgnmm3-rippkgs-indices
declare -gx REPLIT_RTLD_LOADER=1
declare -gx POETRY_PIP_NO_PREFIX=1
declare -gx UV_PYTHON_PREFERENCE=only-system
declare -gx REPLIT_PID1_VERSION=0.0.0-de7d797
declare -gx PROMPT_DIRTRIM=2
declare -gx PIP_CONFIG_FILE=/nix/store/18wg74sr4d4ypyifyzwmakj6z96sshaz-pip.conf
declare -gx npm_config_prefix=/home/<USER>/workspace/.config/npm/node_global
declare -gx XDG_DATA_HOME=/home/<USER>/workspace/.local/share
declare -gx POETRY_VIRTUALENVS_CREATE=0
declare -gx PKG_CONFIG_PATH=/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/lib/pkgconfig:/nix/store/191vca5vdxdlr32k2hpzd66mic98930f-openssl-3.0.13-dev/lib/pkgconfig
declare -gx LANG=en_US.UTF-8
declare -gx REPL_HOME=/home/<USER>/workspace
declare -gx REPL_SLUG=workspace
declare -gx XDG_CACHE_HOME=/home/<USER>/workspace/.cache
declare -gx REPLIT_CLI=/nix/store/qpzxppndmbvaj402j2im09kajky38k03-pid1-0.0.1/bin/replit
declare -gx REPLIT_DOMAINS=5d63f733-8138-476e-bb1d-56c0f7c5cf06-00-28nvn60s8oynr.janeway.replit.dev
declare -gx REPLIT_ENVIRONMENT=production
read -r _new_path <<< "/nix/store/qannz09m66qpcy3ny1f4nkl4ql0g71ks-openssl-3.0.13-bin/bin:/nix/store/vspfg13yjri96m64jv8408mbkjfvr2bb-ping-iputils-20240117/bin:/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/bin:/nix/store/s62s2lf3bdqd0iiprrf3xcks35vkyhpb-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/lyx73qs96hfazl77arnwllwckq9dy012-nodejs-20.18.1-wrapped/bin:/nix/store/917mlm9pvmkd4c62rvvhp2xyd2c9hyl5-bun-1.2.16/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/home/<USER>/workspace/.pythonlibs/bin:/nix/store/wqhkxzzlaswkj3gimqign99sshvllcg6-python-wrapped-0.1.0/bin:/nix/store/72p5niwg4sq9bpk0gpmbxs0xdvkhin1c-pip-wrapper/bin:/nix/store/y2dmnr3gzmxx9znfs1cd6yfrc2f1swgy-poetry-wrapper/bin:/nix/store/kmh81wija3rfppc2nmffwr07j5vmiz9z-uv-0.5.11/bin:/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/5m92sb69w0w73pqa1na2x972yzdjahb6-pid1/bin:/nix/store/a04r09wqgcp9kmgfgmpbqmm20b7ffx7w-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
#PATH=/nix/store/qannz09m66qpcy3ny1f4nkl4ql0g71ks-openssl-3.0.13-bin/bin:/nix/store/vspfg13yjri96m64jv8408mbkjfvr2bb-ping-iputils-20240117/bin:/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/bin:/nix/store/s62s2lf3bdqd0iiprrf3xcks35vkyhpb-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/lyx73qs96hfazl77arnwllwckq9dy012-nodejs-20.18.1-wrapped/bin:/nix/store/917mlm9pvmkd4c62rvvhp2xyd2c9hyl5-bun-1.2.16/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/home/<USER>/workspace/.pythonlibs/bin:/nix/store/wqhkxzzlaswkj3gimqign99sshvllcg6-python-wrapped-0.1.0/bin:/nix/store/72p5niwg4sq9bpk0gpmbxs0xdvkhin1c-pip-wrapper/bin:/nix/store/y2dmnr3gzmxx9znfs1cd6yfrc2f1swgy-poetry-wrapper/bin:/nix/store/kmh81wija3rfppc2nmffwr07j5vmiz9z-uv-0.5.11/bin:/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/5m92sb69w0w73pqa1na2x972yzdjahb6-pid1/bin:/nix/store/a04r09wqgcp9kmgfgmpbqmm20b7ffx7w-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
if [ -e "/run/replit/env/last" ]; then read -r _last_path < <(\grep '^#PATH=' /run/replit/env/last | cut -f 2 -d =); fi
_user_components="$(\tr : $'\n' <<< "${PATH:-}" |\grep -xv -f <(\tr : $'\n' <<< "${_last_path}") |\tr $'\n' :)"
declare -gx PATH="${_user_components}${_new_path}"
declare -gx PYTHONPATH=/nix/store/lc7qv2ldzrs1aq3hbyzmbgvn0h2w26pl-sitecustomize/lib/python/site-packages:/nix/store/dfg63lldbvcj207iyr9z0xw21d8ax02n-python3.11-pip-24.0/lib/python3.11/site-packages
declare -gx POETRY_DOWNLOAD_WITH_CURL=1
declare -gx UV_PYTHON_DOWNLOADS=never
declare -gx GI_TYPELIB_PATH=''
declare -gx REPLIT_NIX_CHANNEL=stable-24_05
declare -gx COLORTERM=truecolor
declare -gx UV_PROJECT_ENVIRONMENT=/home/<USER>/workspace/.pythonlibs
declare -gx POETRY_INSTALLER_MODERN_INSTALLATION=1
declare -gx PYTHONUSERBASE=/home/<USER>/workspace/.pythonlibs
declare -gx REPLIT_LD_LIBRARY_PATH=/nix/store/0wyfjxk51chyzvvzv06aklxdcgdi0256-postgresql-15.7-debug/lib:/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/lib:/nix/store/xb4h083j02mr2ix7pgj7iawxh2hk100l-postgresql-15.7-lib/lib:/nix/store/191vca5vdxdlr32k2hpzd66mic98930f-openssl-3.0.13-dev/lib:/nix/store/vrkxygip635wv4m91b89vbr7hbfih52c-openssl-3.0.13-debug/lib:/nix/store/gp504m4dvw5k2pdx6pccf1km79fkcwgf-openssl-3.0.13/lib
declare -gx NIXPKGS_ALLOW_UNFREE=1
declare -gx REPL_IMAGE=gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9
declare -gx REPLIT_LD_AUDIT=/nix/store/1blcr36jzjnqs8xcnckviaqh1gky3mhw-replit_rtld_loader-1/rtld_loader.so
declare -gx POETRY_PIP_NO_ISOLATE=1
declare -gx POETRY_USE_USER_SITE=1
declare -gx NIX_PATH=nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels
declare -gx __EGL_VENDOR_LIBRARY_FILENAMES=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/share/glvnd/egl_vendor.d/50_mesa.json
declare -gx GLIBC_TUNABLES=glibc.rtld.optional_static_tls=2500
declare -gx POETRY_PIP_USE_PIP_CACHE=1
