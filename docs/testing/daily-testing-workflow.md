# 🧪 Daily Testing Workflow - DatPortal Team Guide

## 🎯 **QUANDO ESEGUIRE I TEST**

### ⚡ **Durante Development (SEMPRE)**
```bash
# Test specifici per la feature su cui stai lavorando
python -m pytest tests/integration/test_business_workflows.py::TestProjectLifecycleWorkflow -v

# Test del modulo che stai modificando
python -m pytest tests/unit/test_project_model.py -v
```

### 🔍 **Prima di Commit (CRITICO)**
```bash
# Test completi integration per verificare non hai rotto nulla
python -m pytest tests/integration/test_business_workflows.py -v
python -m pytest tests/integration/test_ai_services.py -v
```

### 📦 **Prima di Deploy (OBBLIGATORIO)**
```bash
# Full test suite per production readiness
python -m pytest tests/integration/ -v --tb=short
```

## 🚀 **WORKFLOW RACCOMANDATO PER SVILUPPATORI**

### **1. Feature Development Workflow**
```bash
# Step 1: Sviluppi nuova feature
# Step 2: Aggiungi test specifici se necessario
# Step 3: Run test related
python -m pytest tests/integration/test_[relevant_domain].py -v

# Esempio: Se stai lavorando su progetti
python -m pytest tests/integration/test_business_workflows.py::TestProjectLifecycleWorkflow -v
```

### **2. Pre-Commit Check Workflow**
```bash
# Verifica che la tua feature non abbia rotto workflow esistenti
python -m pytest tests/integration/test_business_workflows.py -v

# Se lavori con AI services
python -m pytest tests/integration/test_ai_services.py -v
```

### **3. Pre-Merge Validation**
```bash
# Full validation prima di merge su main
python -m pytest tests/integration/ -v --tb=short
```

## 💡 **STRATEGIA SMART TESTING**

### 🎯 **Test Prioritization**

#### **HIGH PRIORITY** (Sempre da testare)
- Business workflows che toccano **revenue/compliance**
- Calcoli matematici (budget, KPI, timesheet)
- Database model relationships
- API endpoints critici

#### **MEDIUM PRIORITY** (Testare se modificati)
- AI services e integrazione
- User experience workflows
- Cross-module integrations

#### **LOW PRIORITY** (Opzionale)
- Edge cases e performance
- UI components puri
- Documentazione updates

### ⚡ **Fast Feedback Loop**

#### **Quick Validation (30 secondi)**
```bash
# Testa un singolo workflow critico
python -m pytest tests/integration/test_business_workflows.py::TestProjectLifecycleWorkflow::test_complete_project_lifecycle -v
```

#### **Medium Validation (2-3 minuti)**
```bash
# Testa tutto il dominio business
python -m pytest tests/integration/test_business_workflows.py -v
```

#### **Full Validation (5-10 minuti)**
```bash
# Testa tutti i integration tests
python -m pytest tests/integration/ -v
```

## 🔧 **AUTOMATION SETUP**

### **Git Hooks Setup**
```bash
# Pre-commit hook automatico
echo "#!/bin/bash
echo '🧪 Running integration tests...'
python -m pytest tests/integration/test_business_workflows.py -v
if [ \$? -ne 0 ]; then
    echo '❌ Tests failed! Commit blocked.'
    exit 1
fi
echo '✅ Tests passed! Proceeding with commit.'
" > .git/hooks/pre-commit

chmod +x .git/hooks/pre-commit
```

### **CI/CD Pipeline Integration (Futuro)**
```yaml
# .github/workflows/tests.yml
name: DatPortal Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          cd backend
          pip install -r requirements.txt
      
      - name: Run Integration Tests
        run: |
          cd backend
          python -m pytest tests/integration/ -v --tb=short
```

## 📋 **RACCOMANDAZIONI PRATICHE**

### ✅ **SEMPRE Esegui Test Quando**:
- **Modifichi business logic** (calcoli, workflow, formule)
- **Tocchi modelli database** o relationships
- **Cambi API endpoints** o response format
- **Aggiungi nuovi servizi AI** o integrazione
- **Modifichi authentication** o security logic

### ⚠️ **OPZIONALE Ma Consigliato**:
- Modifiche frontend pure (CSS, UI components senza logic)
- Aggiornamenti documentazione
- Refactoring senza behavior changes
- Performance optimizations

### 🚨 **CRITICO Prima Di**:
- **Merge su main branch**
- **Deploy in production**
- **Release di nuove feature ai clienti**
- **Hotfix deployment**

## 🎯 **TEST COMMANDS REFERENCE**

### **Backend Tests**
```bash
# Tutti i business workflows
python -m pytest tests/integration/test_business_workflows.py -v

# Tutti i servizi AI
python -m pytest tests/integration/test_ai_services.py -v

# Test specifico workflow
python -m pytest tests/integration/test_business_workflows.py::TestProjectLifecycleWorkflow -v

# Test con output dettagliato
python -m pytest tests/integration/test_business_workflows.py -v -s

# Test con coverage
python -m pytest tests/integration/ --cov=services --cov=models -v
```

### **Debugging Failed Tests**
```bash
# Test con traceback completo
python -m pytest tests/integration/test_business_workflows.py::TestProjectLifecycleWorkflow::test_complete_project_lifecycle -v -s --tb=long

# Test con PDB debugger
python -m pytest tests/integration/test_business_workflows.py::TestProjectLifecycleWorkflow::test_complete_project_lifecycle -v --pdb
```

## 📊 **QUALITY GATES**

### **Code Quality Checklist**
- [ ] ✅ Business workflows tests passano
- [ ] ✅ AI services tests passano  
- [ ] ✅ Unit tests passano per models modificati
- [ ] ✅ No breaking changes su API
- [ ] ✅ Database migrations testate

### **Pre-Production Checklist**
- [ ] ✅ Full integration test suite passa
- [ ] ✅ Performance tests accettabili
- [ ] ✅ Security validation completata
- [ ] ✅ Manual testing su feature critiche
- [ ] ✅ Documentation aggiornata

## 🚨 **EMERGENCY PROCEDURES**

### **Se i Test Falliscono**
1. **Non ignorare mai i test failing**
2. **Verifica se il fallimento è related alla tua feature**
3. **Se non related, controlla con il team**
4. **Se related, fixa before committing**

### **Hotfix Procedure**
```bash
# Per hotfix urgenti
python -m pytest tests/integration/test_business_workflows.py -v --tb=short

# Se passa, deploy
# Se non passa, fix first
```

## 🎉 **BEST PRACTICES**

### **Regola d'Oro**
> **"Meglio 2 minuti di test ora che 2 ore di debugging domani!"** 🎯

### **Team Guidelines**
1. **Test First**: Pensa ai test quando sviluppi
2. **Fast Feedback**: Usa quick validation durante development
3. **Comprehensive Check**: Full validation prima di merge
4. **Communication**: Comunica test failures al team
5. **Documentation**: Aggiorna test quando aggiungi feature

### **Performance Tips**
- Usa test specifici durante development
- Run full suite solo pre-commit/merge
- Parallelize test execution quando possibile
- Monitor test execution time trends

---

## 📞 **SUPPORTO**

**Per problemi con i test**:
- Controlla `tests/README_TESTING_STRATEGY.md` per dettagli
- Chiedi supporto al team per test complessi
- Documenta nuovi test patterns per il team

**Remember**: I test sono la nostra safety net per delivery di qualità! 🛡️