# ✅ Team Testing Checklist - DatPortal

## 🎯 **QUICK REFERENCE**

### **Development Phase**
- [ ] Run related tests durante coding
- [ ] Add test cases per nuove feature
- [ ] Validate business logic con tests

### **Pre-Commit**
- [ ] Business workflows tests pass
- [ ] AI services tests pass (se modificati)
- [ ] Unit tests pass per models touched

### **Pre-Deploy**
- [ ] Full integration test suite passa
- [ ] No breaking changes detected
- [ ] Manual smoke test su feature critiche

## 🚀 **COMMAND SHORTCUTS**

### **Quick Tests (Development)**
```bash
# Project workflows
python -m pytest tests/integration/test_business_workflows.py::TestProjectLifecycleWorkflow -v

# CRM workflows  
python -m pytest tests/integration/test_business_workflows.py::TestCRMSalesWorkflow -v

# HR workflows
python -m pytest tests/integration/test_business_workflows.py::TestHRPersonnelWorkflow -v

# AI services
python -m pytest tests/integration/test_ai_services.py::TestAIServiceBasic -v
```

### **Medium Tests (Pre-Commit)**
```bash
# All business workflows
python -m pytest tests/integration/test_business_workflows.py -v

# All AI services
python -m pytest tests/integration/test_ai_services.py -v
```

### **Full Tests (Pre-Deploy)**
```bash
# Complete integration suite
python -m pytest tests/integration/ -v --tb=short
```

## 📊 **SUCCESS CRITERIA**

### **Expected Pass Rates**
- ✅ **Business Workflows**: 9/9 tests (100%)
- ✅ **AI Services**: 24/24 tests (100%)
- ✅ **Project CRUD**: 18/18 tests (100%)
- ✅ **Auth & Session**: 2/2 tests (100%)

### **Performance Benchmarks**
- ⚡ Quick tests: < 30 seconds
- 🔍 Medium tests: < 3 minutes
- 📦 Full tests: < 10 minutes

## 🔧 **TROUBLESHOOTING**

### **Common Issues**

#### **Database Connection Errors**
```bash
# Reset test database
python -c "from tests.conftest import reset_test_db; reset_test_db()"
```

#### **Import Errors**
```bash
# Verify Python path
export PYTHONPATH="/home/<USER>/workspace/backend:$PYTHONPATH"
```

#### **Model Relationship Errors**
```bash
# Run model validation
python -m pytest tests/unit/test_*_model.py -v
```

### **When Tests Fail**
1. **Check error message carefully**
2. **Run individual test with verbose output**
3. **Verify database state**
4. **Check for recent model changes**
5. **Ask team if persistent failures**

## 📈 **QUALITY METRICS TO MONITOR**

### **Test Health Indicators**
- Test execution time trends
- Pass/fail rates by domain
- Coverage metrics per module
- Frequency of test updates

### **Business Logic Validation**
- Mathematical calculations accuracy
- Workflow state transitions
- Data integrity checks
- Cross-module integrations

## 🎉 **TEAM BEST PRACTICES**

### **Development Workflow**
1. **Write tests for new business logic**
2. **Update tests when changing behavior**
3. **Run relevant tests during development**
4. **Full validation before commits**

### **Code Review Checklist**
- [ ] Tests added for new functionality
- [ ] Existing tests still pass
- [ ] Business logic validated
- [ ] Edge cases considered

### **Release Readiness**
- [ ] All integration tests pass
- [ ] Performance benchmarks met
- [ ] Security validations complete
- [ ] Manual testing completed

---

**Remember**: Testing is not overhead, it's our **quality insurance policy**! 🛡️