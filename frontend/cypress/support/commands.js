// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom login command
Cypress.Commands.add('login', (username = 'admin', password = 'password') => {
  cy.session([username, password], () => {
    cy.visit('/login')
    cy.get('[data-testid="username"]').type(username)
    cy.get('[data-testid="password"]').type(password)
    cy.get('[data-testid="login-button"]').click()
    cy.url().should('include', '/app/dashboard')
  })
})

// Custom logout command
Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click()
  cy.get('[data-testid="logout-button"]').click()
  cy.url().should('include', '/login')
})

// Command to wait for API requests
Cypress.Commands.add('waitForApi', () => {
  cy.intercept('GET', '/api/**').as('apiRequest')
  cy.wait('@apiRequest', { timeout: 10000 })
})

// Command to mock API responses
Cypress.Commands.add('mockApi', (method, url, response) => {
  cy.intercept(method, url, response)
})

// Command to fill form fields by data-testid
Cypress.Commands.add('fillForm', (formData) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.get(`[data-testid="${field}"]`).clear().type(value)
  })
})

// Command to check if element exists without failing
Cypress.Commands.add('elementExists', (selector) => {
  cy.get('body').then($body => {
    return $body.find(selector).length > 0
  })
})

// Command for checking API response structure
Cypress.Commands.add('validateApiResponse', (alias, expectedStructure) => {
  cy.wait(alias).then((interception) => {
    expect(interception.response.body).to.have.property('success', true)
    if (expectedStructure.data) {
      expect(interception.response.body).to.have.property('data')
      Object.keys(expectedStructure.data).forEach(key => {
        expect(interception.response.body.data).to.have.property(key)
      })
    }
  })
})