{"name": "datportal-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:dev": "start-server-and-test dev http://localhost:5173 'cypress open'", "test:all": "npm run test && npm run test:e2e", "lint": "eslint src --ext .vue,.js"}, "dependencies": {"@heroicons/vue": "^2.2.0", "axios": "^1.6.0", "chart.js": "^4.5.0", "jspdf": "^3.0.1", "marked": "^15.0.12", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@stagewise-plugins/vue": "^0.4.6", "@stagewise/toolbar": "^0.4.8", "@stagewise/toolbar-vue": "^0.4.8", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "@testing-library/vue": "^8.0.0", "@vitejs/plugin-vue": "^4.5.0", "@vitest/ui": "^1.0.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.0.0", "jsdom": "^23.2.0", "postcss": "^8.4.0", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.0", "terser": "^5.43.0", "vite": "^5.0.0", "vitest": "^1.0.0", "vitest-canvas-mock": "^0.3.3"}}